import pathlib
from django.shortcuts import render
from django.http import HttpResponse
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from visits.models import PageVisit
from django.conf import settings
# from channels.layers import get_channel_layer
# from asgiref.sync import async_to_sync

from visits.models import PageVisit

LOGIN_URL = settings.LOGIN_URL

# def home_view(request, *args, **kwargs):
    # qs = PageVisit.objects.all()
    # # this will filter the queryset to only include the path that is equal to the current request path
    # page_qs = PageVisit.objects.filter(path=request.path)
    # my_title = "Home Page"
    # my_context = {
    #     "page_title": my_title,
    #     "page_visit_count": page_qs.count(),
    #     "total_visit_count": qs.count(),
    #     "percentage": page_qs.count() / qs.count() * 100,
    # }
    # path = request.path
    # print("path:", path)
    # html_template = "home.html"
    # # PageVisit.objects.create()
    # PageVisit.objects.create(path=request.path)
    # return render(request, html_template, my_context)

    # return about_view(request, *args, **kwargs)

# def about_view(request, *args, **kwargs):
#     qs = PageVisit.objects.all()
#     # this will filter the queryset to only include the path that is equal to the current request path
#     page_qs = PageVisit.objects.filter(path=request.path)
#     try:
#         percentage = page_qs.count() / qs.count() * 100
#     except ZeroDivisionError:
#         percentage = 0
#     my_title = "Home"
#     my_context = {
#         "page_title": my_title,
#         "page_visit_count": page_qs.count(),
#         "total_visit_count": qs.count(),
#         "percentage": percentage,
#     }
#     path = request.path
#     print("path:", path)
#     html_template = "home.html"
#     # PageVisit.objects.create()
#     PageVisit.objects.create(path=request.path)
#     return render(request, html_template, my_context)

def about_page_view(request, *args, **kwargs):
    my_title = "About"
    qs = PageVisit.objects.all()
    PageVisit.objects.create(path=request.path)
    my_context = {
        "page_title": my_title,
        "page_visit_count": qs.count(),
    }
    html_template = "about.html"
    return render(request, html_template, my_context)

# custom 404 view
def custom_404(request, exception):
    return render(request, '404.html', status=404)


# def test(request):
#     # try:
#         channel_layer = get_channel_layer()
#         async_to_sync(channel_layer.group_send)(
#             "notification_broadcast",
#             {
#                 "type": "send_notification",
#                 "message": "Hello, World!",
#             },
#         )
#     #     # Debugging: Log the request path
#     #     print(f"Request path: {request.path}")
         
#         return HttpResponse("Hello, World!")
#     # except Exception as e:
#     #     # Debugging: Log the exception details
#     #     print(f"Error: {e}")
#         # return HttpResponse(f"Error: {e}", status=500)

VALID_CODE = "abc123"

def pw_protectecd_view(request, *args, **kwargs):
    # is_allowed = False
    is_allowed = request.session.get('protected_page_allowed') or 0
    print(request.session.get('protected_page_allowed'), type(request.session.get('protected_page_allowed')))
    if request.method == "POST":
        entered_code = request.POST.get("code") or None
        if entered_code == VALID_CODE:
            # is_allowed = True
            is_allowed = 1
            request.session['protected_page_allowed'] = is_allowed
    if is_allowed:
        return render(request, "protected/view.html", {})
    else:
        return render(request, "protected/entry.html", {})
    
@login_required(login_url=LOGIN_URL)
def user_only_view(request, *args, **kwargs):
    # we can check if the user active or a staff
    # print(request.user.is_active)
    # print(request.user.is_staff)
    return render(request, "protected/user-only.html", {})

@staff_member_required(login_url=LOGIN_URL)
def staff_only_view(request, *args, **kwargs):
    return render(request, "protected/staff-only.html", {})