document.addEventListener('DOMContentLoaded', function() {
    const addBenefitBtn = document.getElementById('add-benefit-btn');
    const benefitModal = document.getElementById('benefit-modal');
    const popupModal = document.getElementById('popup-modal');
    const benefitForm = document.getElementById('benefit-form');
    const prevButton = document.getElementById('prev-step');
    const nextButton = document.getElementById('next-step');
    const modalTitle = document.getElementById('popup-modal-title');
    const confirmButton = document.getElementById('confirm-modal-button');
    const paymentsTable = document.getElementById('payments-table');
    const searchPaymentsBtn = document.getElementById('search-payments');
    const paymentMonthFilter = document.getElementById('payment-month-filter');

    let currentBenefitId = null;
    let currentAction = null;

    // Initialize modals using Flowbite
    const modalOptions = {
        backdrop: 'dynamic',
        backdropClasses: 'bg-gray-900 bg-opacity-50 dark:bg-opacity-80 fixed inset-0 z-40',
        closable: true
    };
    
    const benefitModalInstance = new Modal(benefitModal, modalOptions);
    const popupModalInstance = new Modal(popupModal, modalOptions);

    function updateSummaryData() {
        const totalContributions = document.getElementById('total-contributions');
        const employerMatch = document.getElementById('employer-match');
        const activeBenefitsCount = document.getElementById('active-benefits-count');

        fetch('/finance/get_benefits_summary/')
            .then(response => response.json())
            .then(data => {
                totalContributions.textContent = data.total_contributions.toFixed(2);
                employerMatch.textContent = data.employer_match.toFixed(2);
                activeBenefitsCount.textContent = data.active_benefits_count;
            });
    }

    // Handle payment search
    searchPaymentsBtn.addEventListener('click', () => {
        const [year, month] = paymentMonthFilter.value.split('-');
        
        fetch(`/finance/get_benefit_payments/${year}/${month}/`)
            .then(response => response.json())
            .then(data => {
                paymentsTable.innerHTML = '';
                data.payments.forEach(payment => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${payment.benefit_name}</td>
                        <td>${payment.payment_date}</td>
                        <td>$${payment.employee_contribution}</td>
                        <td>$${payment.employer_match}</td>
                        <td>
                            <button class="edit-payment btn btn-secondary" data-id="${payment.id}">Edit</button>
                            <button class="delete-payment btn btn-danger" data-id="${payment.id}">Delete</button>
                        </td>
                    `;
                    paymentsTable.appendChild(row);
                });
                attachPaymentEventListeners();
            });
    });

    function attachPaymentEventListeners() {
        // Edit payment buttons
        document.querySelectorAll('.edit-payment').forEach(button => {
            button.addEventListener('click', (e) => {
                const paymentId = e.target.dataset.id;
                fetch(`/finance/get_benefit_payment/${paymentId}/`)
                    .then(response => response.json())
                    .then(data => {
                        // Populate and show payment edit modal
                        // Implementation depends on your modal structure
                    });
            });
        });

        // Delete payment buttons
        document.querySelectorAll('.delete-payment').forEach(button => {
            button.addEventListener('click', (e) => {
                const paymentId = e.target.dataset.id;
                if (confirm('Are you sure you want to delete this payment?')) {
                    fetch(`/finance/delete_benefit_payment/${paymentId}/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            e.target.closest('tr').remove();
                            updateSummaryData();
                        }
                    });
                }
            });
        });
    }

    // Initialize the page
    updateSummaryData();
    attachPaymentEventListeners();

    // Set current month/year in payment filter
    const now = new Date();
    paymentMonthFilter.value = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

    // Show modal for adding new benefit
    addBenefitBtn.addEventListener('click', () => {
        currentBenefitId = null;
        benefitForm.reset();
        benefitModalInstance.show();
    });

    // Handle edit benefit button clicks
    document.querySelectorAll('.btn-edit-benefit').forEach(button => {
        button.addEventListener('click', (e) => {
            const benefitItem = e.target.closest('.benefit-item');
            currentBenefitId = benefitItem.dataset.benefitId;
            
            // Fetch benefit data and populate form
            fetch(`/finance/get_benefit/${currentBenefitId}/`)
                .then(response => response.json())
                .then(data => {
                    Object.keys(data).forEach(key => {
                        const input = benefitForm.querySelector(`[name="${key}"]`);
                        if (input) input.value = data[key];
                    });
                    benefitModalInstance.show();
                });
        });
    });

    // Handle delete benefit button clicks
    document.querySelectorAll('.btn-delete-benefit').forEach(button => {
        button.addEventListener('click', (e) => {
            const benefitItem = e.target.closest('.benefit-item');
            currentBenefitId = benefitItem.dataset.benefitId;
            currentAction = 'delete';
            
            modalTitle.textContent = 'Are you sure you want to delete this benefit?';
            popupModalInstance.show();
        });
    });

    // Handle confirm button click in popup modal
    confirmButton.addEventListener('click', () => {
        if (currentAction === 'delete') {
            fetch(`/finance/delete_benefit/${currentBenefitId}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.reload();
                } else {
                    alert('Failed to delete benefit.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the benefit.');
            });
        }
        popupModalInstance.hide();
    });

    // Handle form submission
    benefitForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const formData = new FormData(benefitForm);
        
        if (currentBenefitId) {
            formData.append('update_benefit', '1');
            formData.append('benefit_id', currentBenefitId);
        } else {
            formData.append('create_benefit', '1');
        }

        fetch('/finance/employer_benefits/', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                window.location.reload();
            } else {
                // Handle errors
                Object.keys(data.errors).forEach(key => {
                    const input = benefitForm.querySelector(`[name="${key}"]`);
                    if (input) {
                        input.classList.add('error');
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'error-message';
                        errorDiv.textContent = data.errors[key];
                        input.parentNode.appendChild(errorDiv);
                    }
                });
            }
        });
    });

    // Navigation
    prevButton.addEventListener('click', () => {
        window.location.href = '/finance/debt_management/';
    });

    nextButton.addEventListener('click', () => {
        window.location.href = '/finance/investment_accounts/';
    });
});
