/* Investment-specific styles */

/* Action badges for transaction types */
.action-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.action-buy {
    background-color: #dcfce7;
    color: #166534;
}

.action-sell {
    background-color: #fee2e2;
    color: #991b1b;
}

.action-short {
    background-color: #fed7aa;
    color: #9a3412;
}

.action-cover {
    background-color: #dbeafe;
    color: #1e40af;
}

.action-dividend_reinvest {
    background-color: #e0e7ff;
    color: #3730a3;
}

.action-stock_split {
    background-color: #f3e8ff;
    color: #6b21a8;
}

.action-option_exercise {
    background-color: #fef3c7;
    color: #92400e;
}

.action-transfer_in {
    background-color: #d1fae5;
    color: #065f46;
}

.action-transfer_out {
    background-color: #fecaca;
    color: #7f1d1d;
}

/* Action cards for quick actions */
.action-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.action-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.action-card .icon-container {
    margin-right: 0.75rem;
    padding: 0.5rem;
    background-color: #f3f4f6;
    border-radius: 0.375rem;
}

/* Portfolio allocation charts */
.allocation-chart {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto;
}

/* Performance indicators */
.performance-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.performance-positive {
    background-color: #dcfce7;
    color: #166534;
}

.performance-negative {
    background-color: #fee2e2;
    color: #991b1b;
}

.performance-neutral {
    background-color: #f3f4f6;
    color: #374151;
}

/* Asset type indicators */
.asset-type-badge {
    display: inline-block;
    padding: 0.125rem 0.5rem;
    background-color: #f3f4f6;
    color: #374151;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.asset-type-stock {
    background-color: #dbeafe;
    color: #1e40af;
}

.asset-type-etf {
    background-color: #dcfce7;
    color: #166534;
}

.asset-type-option {
    background-color: #fef3c7;
    color: #92400e;
}

.asset-type-crypto {
    background-color: #f3e8ff;
    color: #6b21a8;
}

.asset-type-bond {
    background-color: #e0e7ff;
    color: #3730a3;
}

/* Position status indicators */
.position-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.position-status-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
}

.position-long .position-status-dot {
    background-color: #10b981;
}

.position-short .position-status-dot {
    background-color: #ef4444;
}

.position-closed .position-status-dot {
    background-color: #6b7280;
}

/* Investment-specific table styles */
.investments-table {
    width: 100%;
    border-collapse: collapse;
}

.investments-table th,
.investments-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.investments-table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.investments-table tbody tr:hover {
    background-color: #f9fafb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .action-card {
        flex-direction: column;
        text-align: center;
    }
    
    .action-card .icon-container {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .allocation-chart {
        width: 150px;
        height: 150px;
    }
}

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Price change animations */
.price-change {
    transition: all 0.3s ease;
}

.price-up {
    color: #10b981;
    animation: flash-green 0.5s ease;
}

.price-down {
    color: #ef4444;
    animation: flash-red 0.5s ease;
}

@keyframes flash-green {
    0%, 100% { background-color: transparent; }
    50% { background-color: rgba(16, 185, 129, 0.1); }
}

@keyframes flash-red {
    0%, 100% { background-color: transparent; }
    50% { background-color: rgba(239, 68, 68, 0.1); }
}
