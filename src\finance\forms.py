from django import forms
from django.forms import ModelForm
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field
from .models import (MonthlyFinance, BasicFinance, EmergencyFund, Debt, EmployerBenefit, InvestmentAccount, Goals, MonthlyGoalPayment, EmergencyFundContribution, MonthlyDebtPayment, MonthlyEmployerBenefitPayment, MonthlyInvestmentAccountPayment)
import calendar
from datetime import datetime
from helpers.validators import number_input_validation

BOOL_CHOICES = [(True, 'Yes'), (False, 'No')]

class MonthlyFinanceForm(ModelForm):
 
    class Meta:
        model = MonthlyFinance
        exclude = ['user']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Generate month choices with names
        MONTH_CHOICES = [(str(i), calendar.month_name[i]) for i in range(1, 13)]
        MONTH_CHOICES.insert(0, ('', 'Select Month'))  # Add empty default option

        # Generate year choices
        current_year = datetime.now().year
        YEAR_CHOICES = [(str(i), str(i)) for i in reversed(range(current_year - 5, current_year + 1))]
        YEAR_CHOICES.insert(0, ('', 'Select Year'))  # Add empty default option

        # Apply widgets
        self.fields['month'].widget = forms.Select(choices=MONTH_CHOICES, attrs={'class': 'form-control'})
        self.fields['year'].widget = forms.Select(choices=YEAR_CHOICES, attrs={'class': 'form-control'})
        
        # Apply common attributes to all decimal fields
        decimal_fields = ['income', 'rent_or_mortgage', 'food_grocery', 'utilities', 'transportation', 'insurance', 'healthcare', 'entertainment', 'shopping', 'personal_care', 'saving_tax_amount']
        
        for field_name in decimal_fields:
            # Add class to all decimal fields
            self.fields[field_name].widget.attrs.update({'class': 'form-control'})
            # Add validator to all decimal fields
            self.fields[field_name].validators.append(number_input_validation)

            if field_name in ['transportation', 'insurance', 'healthcare', 'entertainment', 'shopping', 'personal_care', 'saving_tax_amount']:
                self.fields[field_name].required = False

    def clean(self):
        cleaned_data = super().clean()

        # Guard clause to check if there are any field-specific errors
        if self.errors:
            return cleaned_data  # Return immediately if there are any errors
        
        # Convert empty strings to default values for non-required decimal fields
        optional_fields = ['transportation', 'insurance', 'healthcare', 'entertainment', 
                          'shopping', 'personal_care', 'saving_tax_amount']
        
        for field in optional_fields:
            if field in cleaned_data and cleaned_data[field] == '':
                cleaned_data[field] = 0.00  # Set default value for blank fields
        
        income = cleaned_data.get('income')
        saving_tax_amount = cleaned_data.get('saving_tax_amount')
 
        # if income is not None and saving_tax_amount is not None:
        if saving_tax_amount and income and saving_tax_amount >= income:
            self.add_error('saving_tax_amount', "Saving tax amount cannot be greater than or equal to income.")

        return cleaned_data
    
class BasicFinanceForm(ModelForm):
    class Meta:
        model = BasicFinance
        exclude = ['user']
        
        widgets = {
            'has_debt': forms.RadioSelect(choices=BOOL_CHOICES),
            'has_employer_match': forms.RadioSelect(choices=BOOL_CHOICES)
        }

        def clean(self):
            cleaned_data = super().clean()

            # Guard clause to check if there are any field-specific errors
            if self.errors:
                return cleaned_data  # Return immediately if there are any errors
            
            has_debt = cleaned_data.get('has_debt')
            has_employer_match = cleaned_data.get('has_employer_match')

            return cleaned_data
    
class EmergencyFundForm(ModelForm):
    class Meta:
        model = EmergencyFund
        fields = ['current_amount', 'emergency_fund_target']
        
    def clean(self):
        cleaned_data = super().clean()
        if self.errors:
            return cleaned_data
        
        current_amount = cleaned_data.get('current_amount')
        emergency_fund_target = cleaned_data.get('emergency_fund_target')
        
        if emergency_fund_target and current_amount > emergency_fund_target:
            self.add_error('current_amount', 'Current amount cannot be greater than target amount')
            
        return cleaned_data
        
class EmergencyFundContributionForm(ModelForm):
    class Meta:
        model = EmergencyFundContribution
        fields = ['contribution_amount', 'contribution_date', 'notes']
        widgets = {
            'contribution_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set today's date as default
        if not self.instance.pk:  # Only for new instances
            self.initial['contribution_date'] = datetime.now().date()
        
        self.fields['contribution_amount'].validators.append(number_input_validation)
        self.fields['notes'].required = False

    def clean(self):
        cleaned_data = super().clean()
        if self.errors:
            return cleaned_data
        
        contribution_amount = cleaned_data.get('contribution_amount')
        if contribution_amount and contribution_amount <= 0:
            self.add_error('contribution_amount', 'Contribution amount must be greater than zero')
            
        return cleaned_data
        
class DebtForm(ModelForm):
    class Meta:
        model = Debt
        exclude = ['user']

        name = forms.CharField(required=True)
        initial_amount = forms.DecimalField(validators=[number_input_validation], required=True)
        interest_rate = forms.DecimalField(validators=[number_input_validation], required=True)
        minimum_payment = forms.DecimalField(validators=[number_input_validation], required=True)
        planned_payment = forms.DecimalField(validators=[number_input_validation], required=True)
        remaining_balance = forms.DecimalField(validators=[number_input_validation], required=True)
        start_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=True)
        estimated_end_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=False)
        is_paid_off = forms.BooleanField(required=False)
        
        def clean(self):
            cleaned_data = super().clean()

            if self.errors:
                return cleaned_data  # Return immediately if there are any errors
            
            name = cleaned_data.get('name')
            initial_amount = cleaned_data.get('initial_amount')
            interest_rate = cleaned_data.get('interest_rate')
            minimum_payment = cleaned_data.get('minimum_payment')
            planned_payment = cleaned_data.get('planned_payment')
            remaining_balance = cleaned_data.get('remaining_balance')
            start_date = cleaned_data.get('start_date')
            estimated_end_date = cleaned_data.get('estimated_end_date')
            is_paid_off = cleaned_data.get('is_paid_off')

            return cleaned_data
        
class MonthlyDebtPaymentForm(ModelForm):
    class Meta:
        model = MonthlyDebtPayment
        exclude = ['monthly_finance', 'debt']
        widgets = {
            'payment_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 2, 'placeholder': 'Optional notes about this payment'})
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['actual_payment'].required = False
        self.fields['interest_paid'].required = True
        self.fields['principal_paid'].required = True
        self.fields['payment_date'].required = True
        
        # Set today as default date
        if not self.instance.pk:
            from datetime import date
            self.fields['payment_date'].initial = date.today()
        
class EmployerBenefitForm(ModelForm):
    class Meta:
        model = EmployerBenefit
        exclude = ['user']
        
        name = forms.CharField(required=True)
        employer_match_type = forms.ChoiceField(choices=EmployerBenefit.ACCOUNT_TYPES, required=True)
        employer_match_max = forms.DecimalField(validators=[number_input_validation], required=False)
        employer_match_percent = forms.DecimalField(validators=[number_input_validation], required=False)
        
        def clean(self):
            cleaned_data = super().clean()
            
            if self.errors:
                return cleaned_data  # Return immediately if there are any errors
            
            name = cleaned_data.get('name')
            employer_match_type = cleaned_data.get('employer_match_type')
            employer_match_max = cleaned_data.get('employer_match_max')
            employer_match_percent = cleaned_data.get('employer_match_percent')

            return cleaned_data

class MonthlyEmployerBenefitPaymentForm(ModelForm):

    class Meta:
        model = MonthlyEmployerBenefitPayment
        exclude = ['monthly_finance', 'employer_benefits']
        
        current_contribution = forms.DecimalField(validators=[number_input_validation], required=False)
        current_contribution_percent = forms.DecimalField(validators=[number_input_validation], required=False)

        def clean(self):
            cleaned_data = super().clean()
            
            if self.errors:
                return cleaned_data  # Return immediately if there are any errors
            
            current_contribution = cleaned_data.get('current_contribution')
            current_contribution_percent = cleaned_data.get('current_contribution_percent')

            return cleaned_data
        
class InvestmentAccountForm(ModelForm):
    
    class Meta:
        model = InvestmentAccount
        exclude = ['user']
        
        account_type = forms.ChoiceField(choices=InvestmentAccount.ACCOUNT_TYPES, required=True)
        current_amount = forms.DecimalField(validators=[number_input_validation], required=True)
        
        def clean(self):
            cleaned_data = super().clean()
            
            if self.errors:
                return cleaned_data  # Return immediately if there are any errors
            
            account_type = cleaned_data.get('account_type')
            current_amount = cleaned_data.get('current_amount')

            return cleaned_data
        
class MonthlyInvestmentAccountPaymentForm(ModelForm):

    class Meta:
        model = MonthlyInvestmentAccountPayment
        exclude = ['monthly_finance', 'investment_account']
        
        contribution_amount = forms.DecimalField(validators=[number_input_validation], required=True)
        contribution_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=True)

        def clean(self):
            cleaned_data = super().clean()
            
            if self.errors:
                return cleaned_data  # Return immediately if there are any errors
            
            contribution_amount = cleaned_data.get('contribution_amount')
            contribution_date = cleaned_data.get('contribution_date')

            return cleaned_data
        
class GoalsForm(ModelForm):
    
    class Meta:
        model = Goals
        exclude = ['user']
        
        name = forms.CharField(required=True)
        target_amount = forms.DecimalField(validators=[number_input_validation], required=True)
        current_amount = forms.DecimalField(validators=[number_input_validation], required=True)
        monthly_contribution = forms.DecimalField(validators=[number_input_validation], required=True)
        start_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=True)
        target_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=True)
        category = forms.ChoiceField(choices=Goals.GOAL_CATEGORIES, required=True)
        is_required = forms.BooleanField(required=False)
        is_achieved = forms.BooleanField(required=False)
        
        def clean(self):
            cleaned_data = super().clean()
                
            if self.errors:
                return cleaned_data  # Return immediately if there are any errors
            
            name = cleaned_data.get('name')
            target_amount = cleaned_data.get('target_amount')
            current_amount = cleaned_data.get('current_amount')
            monthly_contribution = cleaned_data.get('monthly_contribution')
            start_date = cleaned_data.get('start_date')
            target_date = cleaned_data.get('target_date')
            category = cleaned_data.get('category')
            is_required = cleaned_data.get('is_required')
            is_achieved = cleaned_data.get('is_achieved')

            return cleaned_data
        
class MonthlyGoalPaymentForm(ModelForm):

    class Meta:
        model = MonthlyGoalPayment
        exclude = ['monthly_finance', 'goal']
        
        contribution_amount = forms.DecimalField(validators=[number_input_validation], required=True)
        contribution_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}), required=True)

        def clean(self):
            cleaned_data = super().clean()
            
            if self.errors:
                return cleaned_data  # Return immediately if there are any errors
            
            contribution_amount = cleaned_data.get('contribution_amount')
            contribution_date = cleaned_data.get('contribution_date')

            return cleaned_data
