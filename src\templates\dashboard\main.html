{% extends 'dashboard/base.html' %}

{% block head_title %}{{ page_title }} - {{ block.super }}{% endblock head_title %}

{% block content %}

<div class="container mx-auto px-4 py-8">
    {% if latest_finance %}
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 class="text-2xl font-semibold mb-6 text-gray-700 text-center">Your Latest Financial Data</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 px-4">
            <p class="text-gray-600 bg-gray-50 p-3 rounded"><span class="font-medium">Month/Year:</span> {{ latest_finance.month }}/{{ latest_finance.year }}</p>
            <p class="text-gray-600 bg-gray-50 p-3 rounded"><span class="font-medium">Income:</span> ${{ latest_finance.income }}</p>
            <p class="text-gray-600 bg-gray-50 p-3 rounded"><span class="font-medium">Rent/Mortgage:</span> ${{ latest_finance.rent_or_mortgage }}</p>
            <p class="text-gray-600 bg-gray-50 p-3 rounded"><span class="font-medium">Grocery:</span> ${{ latest_finance.food_grocery }}</p>
        </div>
    </div>

        {% if context_data %}
            <div class="bg-white shadow-md rounded-lg p-6 mb-8">
                <h2 class="text-2xl font-semibold mb-6 text-gray-700 text-center">Expense Comparison (% of Income)</h2>
                <div class="mb-12">
                    <h3 class="text-xl font-medium mb-2 text-gray-600 p-3">Your Percentages:</h3>
                    <p class="text-gray-600 bg-gray-50 p-3 rounded"><span class="font-medium">Rent/Mortgage:</span> {{ context_data.user.rent|floatformat:2 }}%</p>
                    <p class="text-gray-600 bg-gray-50 p-3 rounded"><span class="font-medium">Food/Grocery:</span> {{ context_data.user.food|floatformat:2 }}%</p>
                </div>

                <h3 class="text-xl font-medium mb-4 text-gray-600 p-3">Provincial Comparisons:</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for province in context_data.provinces %}
                        <div class="bg-gray-50 p-4 rounded-md">
                            <h4 class="text-lg font-semibold mb-2 text-gray-700">{{ province.name }}</h4>
                            <p class="text-gray-600"><span class="font-medium">Rent/Mortgage:</span> {{ province.rent|floatformat:2 }}% <span class="text-sm">(Diff: {{ province.rent_diff|floatformat:2 }}%)</span></p>
                            <p class="text-gray-600"><span class="font-medium">Food/Grocery:</span> {{ province.food|floatformat:2 }}% <span class="text-sm">(Diff: {{ province.food_diff|floatformat:2 }}%)</span></p>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <div class="bg-white shadow-md rounded-lg p-6">
                <h2 class="text-2xl font-semibold mb-4 text-gray-700 text-center">Visual Comparison</h2>
                {% if plot_div %}
                    {{ plot_div|safe }}
                {% else %}
                    <p class="text-gray-600">Unable to generate comparison chart.</p>
                {% endif %}
            </div>
        {% else %}
            <p class="text-gray-600">Unable to generate comparison data.</p>
        {% endif %}
    {% else %}
        <p class="text-gray-600">No financial data available.</p>
    {% endif %}

    <div class="py-8 px-4 mx-auto max-w-screen-xl sm:py-16 lg:px-6">
        <p class="text-gray-500 dark:text-gray-400">Welcome to PennyMize, your personal finance companion. Our mission is to simplify the complexities of financial planning and help you focus on the next actionable step towards meeting your personal finance goals. We will help you understand your income and expenses, providing clear insights into your spending patterns. You'll be able to compare your habits to best practices, optimize your tax strategy across different account types, and receive straightforward, actionable steps to improve your financial health. With PennyMize, you'll stay motivated and accountable as you work towards your personal finance goals.</p>
    </div>
</div>

{% endblock content %}