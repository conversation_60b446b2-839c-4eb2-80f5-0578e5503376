from django.contrib import admin
from .models import (
    MonthlyFinance,
    BasicFinance,
    EmergencyFund,
    EmergencyFundContribution,
    Debt,
    MonthlyDebtPayment,
    EmployerBenefit,
    MonthlyEmployerBenefitPayment,
    InvestmentAccount,
    MonthlyInvestmentAccountPayment,
    Goals,
    MonthlyGoalPayment
)

@admin.register(MonthlyFinance)
class MonthlyFinanceAdmin(admin.ModelAdmin):
    list_display = ('user', 'month', 'year', 'income', 'created_at', 'updated_at')
    list_filter = ('year', 'month', 'user')
    search_fields = ('user__username', 'user__email')
    ordering = ('-year', '-month')

@admin.register(BasicFinance)
class BasicFinanceAdmin(admin.ModelAdmin):
    list_display = ('user', 'has_debt', 'has_employer_match', 'created_at')
    list_filter = ('has_debt', 'has_employer_match')
    search_fields = ('user__username', 'user__email')

@admin.register(EmergencyFund)
class EmergencyFundAdmin(admin.ModelAdmin):
    list_display = ('user', 'current_amount', 'emergency_fund_target', 'created_at')
    search_fields = ('user__username', 'user__email')

@admin.register(EmergencyFundContribution)
class EmergencyFundContributionAdmin(admin.ModelAdmin):
    list_display = ('user', 'emergency_fund', 'contribution_amount', 'contribution_date')
    list_filter = ('contribution_date',)
    search_fields = ('monthly_finance__user__username', 'emergency_fund__name')

@admin.register(Debt)
class DebtAdmin(admin.ModelAdmin):
    list_display = ('user', 'name', 'initial_amount', 'remaining_balance', 'is_paid_off')
    list_filter = ('is_paid_off',)
    search_fields = ('user__username', 'name')

@admin.register(MonthlyDebtPayment)
class MonthlyDebtPaymentAdmin(admin.ModelAdmin):
    list_display = ('monthly_finance', 'debt', 'actual_payment', 'payment_date')
    list_filter = ('payment_date',)
    search_fields = ('monthly_finance__user__username', 'debt__name')

@admin.register(EmployerBenefit)
class EmployerBenefitAdmin(admin.ModelAdmin):
    list_display = ('user', 'name', 'employer_match_type', 'employer_match_percent')
    list_filter = ('employer_match_type',)
    search_fields = ('user__username', 'name')

@admin.register(MonthlyEmployerBenefitPayment)
class MonthlyEmployerBenefitPaymentAdmin(admin.ModelAdmin):
    list_display = ('monthly_finance', 'employer_benefits', 'current_contribution', 'current_contribution_percent')
    search_fields = ('monthly_finance__user__username', 'employer_benefits__name')

@admin.register(InvestmentAccount)
class InvestmentAccountAdmin(admin.ModelAdmin):
    list_display = ('user', 'account_type', 'current_amount', 'created_at')
    list_filter = ('account_type',)
    search_fields = ('user__username',)

@admin.register(MonthlyInvestmentAccountPayment)
class MonthlyInvestmentAccountPaymentAdmin(admin.ModelAdmin):
    list_display = ('monthly_finance', 'investment_account', 'contribution_amount')
    search_fields = ('monthly_finance__user__username',)

@admin.register(Goals)
class GoalsAdmin(admin.ModelAdmin):
    list_display = ('user', 'name', 'target_amount', 'target_date')
    search_fields = ('user__username', 'name')

@admin.register(MonthlyGoalPayment)
class MonthlyGoalPaymentAdmin(admin.ModelAdmin):
    list_display = ('monthly_finance', 'goal', 'contribution_amount', 'contribution_date')
    list_filter = ('contribution_date',)
    search_fields = ('monthly_finance__user__username', 'goal__name')
