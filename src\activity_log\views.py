from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from django.utils.timezone import now


from django.http import JsonResponse
from django.contrib.auth import logout
from django.utils import timezone
from datetime import datetime


# @csrf_exempt
# def extend_session(request):
#     if request.method == 'POST' and request.user.is_authenticated:
#         request.session['last_activity'] = now().isoformat()
#         return JsonResponse({'message': 'Session extended'}, status=200)
#     return JsonResponse({'message': 'Failed to extend session'}, status=400)


def extend_session(request):
    request.session.set_expiry(120)  # Reset to 30 minutes
    return JsonResponse({'success': True})

def check_session(request):
    if not request.user.is_authenticated:
        return JsonResponse({'valid': False})
    
    last_activity = request.session.get('last_activity')
    if last_activity:
        inactivity_period = (timezone.now() - datetime.fromisoformat(last_activity)).total_seconds()
        if inactivity_period > 120:  # 30 minutes
            logout(request)
            return JsonResponse({'valid': False})
    
    request.session['last_activity'] = timezone.now().isoformat()
    return JsonResponse({'valid': True})