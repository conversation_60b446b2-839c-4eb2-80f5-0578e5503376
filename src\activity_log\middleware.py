import logging
from datetime import datetime, timedelta
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.contrib.gis.geoip2 import GeoIP2
from django.http import JsonResponse
from django.utils.timezone import now
from .models import ActivityLog

logger = logging.getLogger(__name__)

class ActivityAndSessionMiddleware(MiddlewareMixin):
    def process_request(self, request):
        if not request.user.is_staff and not request.user.is_superuser:
            ip_address = self.get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            page = request.path
            user = request.user if request.user.is_authenticated else None
            session_key = request.session.session_key

            # Get location data
            location = self.get_location(ip_address)

            # Log activity
            ActivityLog.objects.create(
                ip_address=ip_address,
                user_agent=user_agent,
                page=page,
                user=user,
                session_key=session_key,
                location=location
            )

            # Handle session management
            self.handle_session_timeout(request)

    def handle_session_timeout(self, request):
        session_expiry_minutes = getattr(settings, 'SESSION_EXPIRY_MINUTES', 3)
        now_time = now()
        
        last_activity = request.session.get('last_activity')
        if last_activity:
            elapsed_time = (now_time - datetime.fromisoformat(last_activity)).total_seconds() / 60.0
            if elapsed_time > session_expiry_minutes:
                request.session.flush()
                if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                # if request.is_ajax():
                    return JsonResponse({'message': 'Session expired'}, status=401)
        
        request.session['last_activity'] = now_time.isoformat()

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def get_location(self, ip_address):
        try:
            geo = GeoIP2()
            location = geo.city(ip_address)
            return f"{location['city']}, {location['country_name']}"
        except Exception as e:
            logger.error(f"Could not get location for IP {ip_address}: {e}")
            return None
