/**
 * Reusable alert system using Flowbite modals
 */
const AlertSystem = {
    // /**
    //  * Show a success modal in the center of the screen
    //  * @param {string} message - The success message to display
    //  * @param {Function} callback - Optional callback after user clicks continue
    //  */
    showSuccess: function(message, callback) {
        // Create modal if it doesn't exist
        if (!document.getElementById('successModal')) {
            this.createSuccessModal();
        }
        
        // Update message - use innerHTML instead of textContent to support HTML in messages
        document.querySelector('#successModal .message-text').innerHTML = message || 'Operation completed successfully';
        
        // Initialize modal using Flowbite
        const successModal = document.getElementById('successModal');

        // First make sure modal is in the DOM and visible
        successModal.classList.remove('hidden');
        successModal.classList.add('flex');
        
        // Get the modal instance
        let modalInstance;
        try {
            // Try to use Flowbite's Modal constructor if available
            if (typeof Modal !== 'undefined') {
                modalInstance = new Modal(successModal);
                modalInstance.show();
            }
        } catch (e) {
            console.error('Error initializing modal with Flowbite:', e);
        }
        
        // Handle continue button
        const continueBtn = document.getElementById('success-continue-btn');
        const closeBtn = document.getElementById('success-close-btn');
        
        // Define the close handler function
        const handleClose = () => {
            if (modalInstance) {
                try {
                    modalInstance.hide();
                } catch (e) {
                    console.error('Error hiding modal:', e);
                }
            }
            
            // Always ensure the modal is hidden in the DOM
            successModal.classList.add('hidden');
            successModal.classList.remove('flex');
            
            // Call callback if provided
            if (typeof callback === 'function') {
                setTimeout(callback, 100); // Small delay to ensure modal is hidden first
            }
        };
        
        // Use direct event handlers and ensure they're removed first
        if (continueBtn) {
            // Remove existing event listeners by cloning the node
            const newContinueBtn = continueBtn.cloneNode(true);
            continueBtn.parentNode.replaceChild(newContinueBtn, continueBtn);
            newContinueBtn.addEventListener('click', handleClose);
        }
        
        if (closeBtn) {
            // Remove existing event listeners by cloning the node
            const newCloseBtn = closeBtn.cloneNode(true);
            closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);
            newCloseBtn.addEventListener('click', handleClose);
        }
        
        // Add keyboard event listener for Escape key
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                handleClose();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
    },
    
    // /**
    //  * Show an error modal in the center of the screen
    //  * @param {string} message - The error message to display
    //  * @param {Function} callback - Optional callback after user dismisses error
    //  */
    showError: function(message, callback) {
        // Create modal if it doesn't exist
        if (!document.getElementById('errorModal')) {
            this.createErrorModal();
        }
        
        // Update message - use innerHTML instead of textContent to support HTML in messages
        document.querySelector('#errorModal .message-text').innerHTML = message || 'An error occurred';
        
        // Initialize modal using Flowbite
        const errorModal = document.getElementById('errorModal');
        
        // First make sure modal is in the DOM and visible
        errorModal.classList.remove('hidden');
        errorModal.classList.add('flex');
        
        // Get the modal instance
        let modalInstance;
        try {
            // Try to use Flowbite's Modal constructor
            if (typeof Modal !== 'undefined') {
                modalInstance = new Modal(errorModal);
                modalInstance.show();
            }
        } catch (e) {
            console.error('Error initializing modal with Flowbite:', e);
        }
        
        // Handle dismiss button
        const dismissBtn = document.getElementById('error-dismiss-btn');
        const closeBtn = document.getElementById('error-close-btn');
        
        // Define the close handler function
        const handleClose = () => {
            if (modalInstance) {
                try {
                    modalInstance.hide();
                } catch (e) {
                    console.error('Error hiding modal:', e);
                }
            }
            
            // Always ensure the modal is hidden in the DOM
            errorModal.classList.add('hidden');
            errorModal.classList.remove('flex');
            
            // Call callback if provided
            if (typeof callback === 'function') {
                setTimeout(callback, 100); // Small delay to ensure modal is hidden first
            }
        };
        
        // Use direct event handlers and ensure they're removed first
        if (dismissBtn) {
            // Remove existing event listeners by cloning the node
            const newDismissBtn = dismissBtn.cloneNode(true);
            dismissBtn.parentNode.replaceChild(newDismissBtn, dismissBtn);
            newDismissBtn.addEventListener('click', handleClose);
        }
        
        if (closeBtn) {
            // Remove existing event listeners by cloning the node
            const newCloseBtn = closeBtn.cloneNode(true);
            closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);
            newCloseBtn.addEventListener('click', handleClose);
        }
        
        // Add keyboard event listener for Escape key
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                handleClose();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
    },
    
    /**
     * Create success modal HTML structure
     */
    createSuccessModal: function() {
        const modalHTML = `
            <div id="successModal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-[9999] justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                        <button type="button" id="success-close-btn" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                        <div class="p-4 md:p-5 text-center">
                            <div class="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900 p-2 flex items-center justify-center mx-auto mb-3.5">
                                <svg aria-hidden="true" class="w-8 h-8 text-green-500 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                                <span class="sr-only">Success</span>
                            </div>
                            <p class="mb-4 text-lg font-semibold text-gray-900 dark:text-white message-text">Operation completed successfully.</p>
                            <button id="success-continue-btn" type="button" class="py-2 px-3 text-sm font-medium text-center text-white rounded-lg bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:focus:ring-primary-900">
                                Continue
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    },
    
    /**
     * Create error modal HTML structure
     */
    createErrorModal: function() {
        const modalHTML = `
            <div id="errorModal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-[9999] justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                        <button type="button" id="error-close-btn" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                        <div class="p-4 md:p-5 text-center">
                            <div class="w-12 h-12 rounded-full bg-red-100 dark:bg-red-900 p-2 flex items-center justify-center mx-auto mb-3.5">
                                <svg aria-hidden="true" class="w-8 h-8 text-red-500 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                                <span class="sr-only">Error</span>
                            </div>
                            <p class="mb-4 text-lg font-semibold text-gray-900 dark:text-white message-text">An error occurred.</p>
                            <button id="error-dismiss-btn" type="button" class="py-2 px-3 text-sm font-medium text-center text-white rounded-lg bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-900">
                                Dismiss
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
};


