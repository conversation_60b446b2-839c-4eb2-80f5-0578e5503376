from django.db import models
from django.conf import settings
from helpers.validators import number_input_validation
from django.urls import reverse
from decimal import Decimal
from django.utils import timezone


User = settings.AUTH_USER_MODEL

class Brokerage(models.Model):
    name = models.CharField(max_length=100, unique=True)  # e.g., Interactive Brokers, Coinbase
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)  # Optional link to app user
    account_number = models.CharField(max_length=50, blank=True, null=True)  # Account identifier
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Brokerages"

class Currency(models.Model):
    code = models.CharField(max_length=4, unique=True)  # e.g., USD, CAD
    name = models.Char<PERSON><PERSON>(max_length=50, blank=True, null=True)  # e.g., US Dollar
    symbol = models.CharField(max_length=5, blank=True, null=True)  # e.g., $

    def __str__(self):
        return self.code

    class Meta:
        verbose_name_plural = "Currencies"

class CurrencyConversionRate(models.Model):
    from_currency = models.ForeignKey(Currency, related_name='from_rates', on_delete=models.CASCADE)
    to_currency = models.ForeignKey(Currency, related_name='to_rates', on_delete=models.CASCADE)
    rate = models.DecimalField(max_digits=15, decimal_places=8)  # User-provided rate
    date = models.DateField(default=timezone.now)  # Historical rates for accuracy
    source = models.CharField(max_length=50, default='manual')  # manual, api, etc.

    class Meta:
        unique_together = ('from_currency', 'to_currency', 'date')

    def __str__(self):
        return f"{self.from_currency} to {self.to_currency}: {self.rate} on {self.date}"

class AssetClassChoices(models.TextChoices):
    EQUITY = 'EQUITY', 'Equity (Stocks, ETFs, REITs, ADRs)'
    FIXED_INCOME = 'FIXED_INCOME', 'Fixed Income (Bonds, CDs, Treasuries)'
    DERIVATIVE = 'DERIVATIVE', 'Derivative (Options, Futures)'
    ALTERNATIVE = 'ALTERNATIVE', 'Alternative (Crypto, Commodities, Private Equity)'
    CASH = 'CASH', 'Cash & Cash Equivalents'
    OTHER = 'OTHER', 'Other'

class AssetTypeChoices(models.TextChoices):
    # Equity types
    STOCK = 'STOCK', 'Stock'
    ETF = 'ETF', 'Exchange Traded Fund'
    REIT = 'REIT', 'Real Estate Investment Trust'
    ADR = 'ADR', 'American Depositary Receipt'

    # Fixed Income types
    BOND = 'BOND', 'Bond'
    CD = 'CD', 'Certificate of Deposit'
    TREASURY = 'TREASURY', 'Treasury Security'

    # Derivative types
    OPTION = 'OPTION', 'Option'
    FUTURE = 'FUTURE', 'Future'
    WARRANT = 'WARRANT', 'Warrant'

    # Alternative types
    CRYPTO = 'CRYPTO', 'Cryptocurrency'
    COMMODITY = 'COMMODITY', 'Commodity'
    PRIVATE_EQUITY = 'PRIVATE_EQUITY', 'Private Equity'

    # Cash types
    CASH = 'CASH', 'Cash'
    MONEY_MARKET = 'MONEY_MARKET', 'Money Market'

    # Other
    OTHER = 'OTHER', 'Other'

class Asset(models.Model):
    ticker = models.CharField(max_length=20, unique=True)  # e.g., AAPL, BTCUSD
    name = models.CharField(max_length=200)
    asset_class = models.CharField(max_length=20, choices=AssetClassChoices.choices, null=True, blank=True)
    asset_type = models.CharField(max_length=20, choices=AssetTypeChoices.choices, null=True, blank=True)
    sector = models.CharField(max_length=50, blank=True, null=True)  # e.g., Technology
    industry = models.CharField(max_length=100, blank=True, null=True)  # e.g., Software
    country = models.CharField(max_length=50, blank=True, null=True)  # e.g., United States
    exchange = models.CharField(max_length=20, blank=True, null=True)  # e.g., NYSE, NASDAQ
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # For derivatives - underlying asset
    underlying_asset = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)

    # For options
    option_type = models.CharField(max_length=4, choices=[('CALL', 'Call'), ('PUT', 'Put')], null=True, blank=True)
    strike_price = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    expiration_date = models.DateField(null=True, blank=True)

    # For futures
    contract_size = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)

    # Additional metadata
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.asset_type == 'OPTION':
            return f"{self.ticker} - {self.name} ({self.option_type} ${self.strike_price} {self.expiration_date})"
        return f"{self.ticker} - {self.name}"

    class Meta:
        ordering = ['ticker']
    
class Owner(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)  # Link to Django User
    name = models.CharField(max_length=100)
    email = models.EmailField(blank=True, null=True)
    tax_id = models.CharField(max_length=20, blank=True, null=True)  # SSN, SIN, etc.
    default_currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class DividendIncome(models.Model):
    """Track dividend and interest income"""
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE)
    brokerage = models.ForeignKey(Brokerage, on_delete=models.CASCADE)
    date = models.DateField()
    amount_per_share = models.DecimalField(max_digits=15, decimal_places=6)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT)
    income_type = models.CharField(max_length=20, choices=[
        ('DIVIDEND', 'Dividend'),
        ('INTEREST', 'Interest'),
        ('DISTRIBUTION', 'Distribution'),
        ('CAPITAL_GAIN', 'Capital Gain Distribution'),
        ('RETURN_OF_CAPITAL', 'Return of Capital'),
        ('OTHER', 'Other')
    ])
    is_reinvested = models.BooleanField(default=False)
    tax_withheld = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.asset.ticker} - {self.income_type} ${self.total_amount} on {self.date}"

    class Meta:
        ordering = ['-date']

class DividendOwnership(models.Model):
    """Track ownership splits for dividend income"""
    dividend = models.ForeignKey(DividendIncome, on_delete=models.CASCADE, related_name='ownerships')
    owner = models.ForeignKey(Owner, on_delete=models.CASCADE)
    percentage = models.DecimalField(max_digits=5, decimal_places=2)  # e.g., 50.00 for 50%

    class Meta:
        unique_together = ('dividend', 'owner')
        constraints = [
            models.CheckConstraint(
                check=models.Q(percentage__gte=0) & models.Q(percentage__lte=100),
                name='dividend_percentage_range'
            )
        ]

    def __str__(self):
        return f"{self.owner} owns {self.percentage}% of {self.dividend}"
    
class TransactionTypeChoices(models.TextChoices):
    BUY = 'BUY', 'Buy'
    SELL = 'SELL', 'Sell'
    SHORT = 'SHORT', 'Short Sell'
    COVER = 'COVER', 'Cover Short'
    DIVIDEND_REINVEST = 'DIVIDEND_REINVEST', 'Dividend Reinvestment'
    STOCK_SPLIT = 'STOCK_SPLIT', 'Stock Split'
    STOCK_DIVIDEND = 'STOCK_DIVIDEND', 'Stock Dividend'
    SPIN_OFF = 'SPIN_OFF', 'Spin-off'
    MERGER = 'MERGER', 'Merger'
    OPTION_EXERCISE = 'OPTION_EXERCISE', 'Option Exercise'
    OPTION_ASSIGNMENT = 'OPTION_ASSIGNMENT', 'Option Assignment'
    OPTION_EXPIRATION = 'OPTION_EXPIRATION', 'Option Expiration'
    TRANSFER_IN = 'TRANSFER_IN', 'Transfer In'
    TRANSFER_OUT = 'TRANSFER_OUT', 'Transfer Out'

class Transaction(models.Model):
    # Core transaction data
    brokerage = models.ForeignKey(Brokerage, on_delete=models.CASCADE)
    asset = models.ForeignKey(Asset, on_delete=models.PROTECT)
    date = models.DateTimeField(default=timezone.now)
    action = models.CharField(max_length=20, choices=TransactionTypeChoices.choices)
    quantity = models.DecimalField(max_digits=15, decimal_places=6)
    price = models.DecimalField(max_digits=15, decimal_places=6)
    fees = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # Additional transaction details
    commission = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    sec_fee = models.DecimalField(max_digits=15, decimal_places=2, default=0)  # SEC fees
    other_fees = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    accrued_interest = models.DecimalField(max_digits=15, decimal_places=2, default=0)  # For bonds

    # Trade execution details
    execution_time = models.TimeField(null=True, blank=True)
    order_type = models.CharField(max_length=20, choices=[
        ('MARKET', 'Market'),
        ('LIMIT', 'Limit'),
        ('STOP', 'Stop'),
        ('STOP_LIMIT', 'Stop Limit'),
        ('OTHER', 'Other')
    ], default='MARKET')

    # Position tracking
    is_short = models.BooleanField(default=False)
    is_opening = models.BooleanField(default=True)  # Opening vs closing position

    # Related transactions (for complex trades)
    related_transaction = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True)

    # Trade journaling
    notes = models.TextField(blank=True, null=True)
    trade_reason = models.TextField(blank=True, null=True)  # Why this trade was made

    # System fields
    external_id = models.CharField(max_length=100, blank=True, null=True)  # Broker's transaction ID
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def total_cost(self):
        """Calculate total cost including all fees"""
        return abs(self.quantity * self.price) + self.fees + self.commission + self.sec_fee + self.other_fees

    @property
    def net_amount(self):
        """Calculate net amount (positive for money in, negative for money out)"""
        base_amount = self.quantity * self.price
        total_fees = self.fees + self.commission + self.sec_fee + self.other_fees

        if self.action in ['BUY', 'COVER']:
            return -(abs(base_amount) + total_fees)  # Money out
        elif self.action in ['SELL', 'SHORT']:
            return abs(base_amount) - total_fees  # Money in
        else:
            return base_amount  # For other transaction types

    def save(self, *args, **kwargs):
        # Ensure quantity sign is correct based on action
        if self.action in ('SELL', 'COVER', 'TRANSFER_OUT'):
            self.quantity = -abs(self.quantity)
        elif self.action in ('BUY', 'SHORT', 'TRANSFER_IN', 'DIVIDEND_REINVEST'):
            self.quantity = abs(self.quantity)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.action} {self.quantity} {self.asset.ticker} @ ${self.price} on {self.date.date()}"

    class Meta:
        ordering = ['-date']

class TransactionOwnership(models.Model):
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='ownerships')
    owner = models.ForeignKey(Owner, on_delete=models.CASCADE)
    percentage = models.DecimalField(max_digits=5, decimal_places=2)  # e.g., 50.00 for 50%
    amount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)  # Dollar amount owned

    class Meta:
        unique_together = ('transaction', 'owner')
        constraints = [
            models.CheckConstraint(
                check=models.Q(percentage__gte=0) & models.Q(percentage__lte=100),
                name='transaction_percentage_range'
            )
        ]

    def __str__(self):
        return f"{self.owner} owns {self.percentage}% of {self.transaction}"

class TaxLotMethodChoices(models.TextChoices):
    FIFO = 'FIFO', 'First In, First Out'
    LIFO = 'LIFO', 'Last In, First Out'
    HIFO = 'HIFO', 'Highest In, First Out'
    SPECIFIC = 'SPECIFIC', 'Specific Identification'
    AVERAGE = 'AVERAGE', 'Average Cost'

class TaxLot(models.Model):
    """Track individual tax lots for cost basis calculations"""
    owner = models.ForeignKey(Owner, on_delete=models.CASCADE)
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE)
    brokerage = models.ForeignKey(Brokerage, on_delete=models.CASCADE)

    # Purchase details
    purchase_transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='tax_lots_created')
    purchase_date = models.DateTimeField()
    purchase_price = models.DecimalField(max_digits=15, decimal_places=6)
    original_quantity = models.DecimalField(max_digits=15, decimal_places=6)
    remaining_quantity = models.DecimalField(max_digits=15, decimal_places=6)

    # Cost basis tracking
    cost_basis_per_share = models.DecimalField(max_digits=15, decimal_places=6)
    total_cost_basis = models.DecimalField(max_digits=15, decimal_places=2)

    # Adjustments (for splits, dividends, etc.)
    adjustment_factor = models.DecimalField(max_digits=10, decimal_places=6, default=1.0)

    # Status
    is_closed = models.BooleanField(default=False)
    is_short = models.BooleanField(default=False)

    # System fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def current_cost_basis_per_share(self):
        """Cost basis per share after adjustments"""
        return self.cost_basis_per_share / self.adjustment_factor

    @property
    def current_total_cost_basis(self):
        """Total cost basis for remaining quantity"""
        return self.remaining_quantity * self.current_cost_basis_per_share

    def __str__(self):
        return f"{self.asset.ticker} - {self.remaining_quantity} shares @ ${self.current_cost_basis_per_share}"

    class Meta:
        ordering = ['purchase_date']

class TaxLotSale(models.Model):
    """Track which tax lots were used for sales"""
    tax_lot = models.ForeignKey(TaxLot, on_delete=models.CASCADE, related_name='sales')
    sale_transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='tax_lot_sales')
    quantity_sold = models.DecimalField(max_digits=15, decimal_places=6)
    sale_price = models.DecimalField(max_digits=15, decimal_places=6)
    cost_basis_used = models.DecimalField(max_digits=15, decimal_places=2)

    # Calculated fields
    proceeds = models.DecimalField(max_digits=15, decimal_places=2)
    realized_gain_loss = models.DecimalField(max_digits=15, decimal_places=2)

    # Tax classification
    is_long_term = models.BooleanField()  # Held > 1 year

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Calculate proceeds and gain/loss
        self.proceeds = self.quantity_sold * self.sale_price
        self.realized_gain_loss = self.proceeds - self.cost_basis_used

        # Determine if long-term (held > 1 year)
        holding_period = self.sale_transaction.date.date() - self.tax_lot.purchase_date.date()
        self.is_long_term = holding_period.days > 365

        super().save(*args, **kwargs)

    def __str__(self):
        return f"Sale of {self.quantity_sold} {self.tax_lot.asset.ticker} - G/L: ${self.realized_gain_loss}"

class Portfolio(models.Model):
    """Portfolio-level tracking and settings"""
    owner = models.ForeignKey(Owner, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    base_currency = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # Tax lot method preference
    default_tax_lot_method = models.CharField(
        max_length=10,
        choices=TaxLotMethodChoices.choices,
        default=TaxLotMethodChoices.FIFO
    )

    # Portfolio settings
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.owner.name} - {self.name}"

    class Meta:
        unique_together = ('owner', 'name')

class Position(models.Model):
    """Current position summary for each asset"""
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='positions')
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE)
    brokerage = models.ForeignKey(Brokerage, on_delete=models.CASCADE)

    # Position details
    quantity = models.DecimalField(max_digits=15, decimal_places=6, default=0)
    average_cost = models.DecimalField(max_digits=15, decimal_places=6, default=0)
    total_cost_basis = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Market value (updated periodically)
    current_price = models.DecimalField(max_digits=15, decimal_places=6, null=True, blank=True)
    market_value = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    last_price_update = models.DateTimeField(null=True, blank=True)

    # P&L tracking
    unrealized_gain_loss = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    realized_gain_loss = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_dividends = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Position flags
    is_short = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    # System fields
    last_updated = models.DateTimeField(auto_now=True)

    @property
    def unrealized_gain_loss_percent(self):
        """Unrealized gain/loss as percentage"""
        if self.total_cost_basis and self.total_cost_basis != 0:
            return (self.unrealized_gain_loss / self.total_cost_basis) * 100
        return 0

    @property
    def total_return(self):
        """Total return including realized and unrealized gains plus dividends"""
        return self.realized_gain_loss + self.unrealized_gain_loss + self.total_dividends

    @property
    def total_return_percent(self):
        """Total return as percentage"""
        if self.total_cost_basis and self.total_cost_basis != 0:
            return (self.total_return / self.total_cost_basis) * 100
        return 0

    def update_market_value(self, current_price):
        """Update market value and unrealized P&L"""
        self.current_price = current_price
        self.market_value = self.quantity * current_price
        self.unrealized_gain_loss = self.market_value - self.total_cost_basis
        self.last_price_update = timezone.now()
        self.save()

    def __str__(self):
        return f"{self.asset.ticker} - {self.quantity} shares in {self.portfolio.name}"

    class Meta:
        unique_together = ('portfolio', 'asset', 'brokerage')
        ordering = ['asset__ticker']

class PerformanceSnapshot(models.Model):
    """Daily/periodic portfolio performance snapshots"""
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='performance_snapshots')
    date = models.DateField()

    # Portfolio values
    total_value = models.DecimalField(max_digits=20, decimal_places=2)
    total_cost_basis = models.DecimalField(max_digits=20, decimal_places=2)
    cash_balance = models.DecimalField(max_digits=20, decimal_places=2, default=0)

    # Performance metrics
    total_return = models.DecimalField(max_digits=20, decimal_places=2)
    total_return_percent = models.DecimalField(max_digits=10, decimal_places=4)
    day_change = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    day_change_percent = models.DecimalField(max_digits=10, decimal_places=4, default=0)

    # Risk metrics
    volatility = models.DecimalField(max_digits=10, decimal_places=6, null=True, blank=True)
    sharpe_ratio = models.DecimalField(max_digits=10, decimal_places=6, null=True, blank=True)
    max_drawdown = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)

    # Benchmark comparison
    benchmark_return = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)
    alpha = models.DecimalField(max_digits=10, decimal_places=6, null=True, blank=True)
    beta = models.DecimalField(max_digits=10, decimal_places=6, null=True, blank=True)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.portfolio.name} - {self.date} - ${self.total_value}"

    class Meta:
        unique_together = ('portfolio', 'date')
        ordering = ['-date']

class Alert(models.Model):
    """Price and portfolio alerts"""
    owner = models.ForeignKey(Owner, on_delete=models.CASCADE)
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, null=True, blank=True)
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, null=True, blank=True)

    alert_type = models.CharField(max_length=40, choices=[
        ('PRICE_ABOVE', 'Price Above'),
        ('PRICE_BELOW', 'Price Below'),
        ('PERCENT_GAIN', 'Percentage Gain'),
        ('PERCENT_LOSS', 'Percentage Loss'),
        ('PORTFOLIO_VALUE', 'Portfolio Value'),
        ('DIVIDEND_ANNOUNCEMENT', 'Dividend Announcement'),
        ('EARNINGS_DATE', 'Earnings Date'),
        ('OPTION_EXPIRATION', 'Option Expiration'),
        ('MARGIN_CALL', 'Margin Call'),
        ('OTHER', 'Other')
    ])

    # Alert conditions
    threshold_value = models.DecimalField(max_digits=15, decimal_places=6, null=True, blank=True)
    threshold_percent = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)

    # Alert settings
    is_active = models.BooleanField(default=True)
    is_triggered = models.BooleanField(default=False)
    trigger_once = models.BooleanField(default=True)  # Trigger only once or repeatedly

    # Notification preferences
    email_notification = models.BooleanField(default=True)
    sms_notification = models.BooleanField(default=False)
    push_notification = models.BooleanField(default=True)

    # Alert details
    title = models.CharField(max_length=200)
    message = models.TextField(blank=True, null=True)

    # System fields
    created_at = models.DateTimeField(default=timezone.now)
    triggered_at = models.DateTimeField(null=True, blank=True)
    last_checked = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.title} - {self.owner.name}"

    class Meta:
        ordering = ['-created_at']

class CashTransaction(models.Model):
    """Track cash deposits, withdrawals, and transfers"""
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='cash_transactions')
    brokerage = models.ForeignKey(Brokerage, on_delete=models.CASCADE)

    transaction_type = models.CharField(max_length=20, choices=[
        ('DEPOSIT', 'Deposit'),
        ('WITHDRAWAL', 'Withdrawal'),
        ('TRANSFER_IN', 'Transfer In'),
        ('TRANSFER_OUT', 'Transfer Out'),
        ('INTEREST', 'Interest'),
        ('FEE', 'Fee'),
        ('DIVIDEND', 'Dividend Payment'),
        ('OTHER', 'Other')
    ])

    amount = models.DecimalField(max_digits=15, decimal_places=2)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT)
    date = models.DateTimeField()
    description = models.CharField(max_length=200, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)

    # Related transaction (if applicable)
    related_transaction = models.ForeignKey(Transaction, on_delete=models.SET_NULL, null=True, blank=True)
    related_dividend = models.ForeignKey(DividendIncome, on_delete=models.SET_NULL, null=True, blank=True)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.transaction_type} ${self.amount} - {self.date.date()}"

    class Meta:
        ordering = ['-date']