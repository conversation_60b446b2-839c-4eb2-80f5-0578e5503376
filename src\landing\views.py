from django.shortcuts import render
from visits.models import PageVisit
from dashboard.views import dashboard_view

def landing_dashboard_page_view(request):
    if request.user.is_authenticated:
        return dashboard_view(request)
    page_title = "Home"
    qs = PageVisit.objects.all()
    PageVisit.objects.create(path=request.path)
    my_context = {
        "page_title": page_title,
        "page_visit_count": qs.count(),
    }
    html_template = "landing/main.html"
    return render(request, html_template, my_context)
