from django.conf import settings
from django.db import models
from helpers.validators import number_input_validation
from django.urls import reverse
    
User = settings.AUTH_USER_MODEL

class MoneyField(models.DecimalField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('max_digits', 10)
        kwargs.setdefault('decimal_places', 2)
        kwargs.setdefault('validators', [number_input_validation])
        super().__init__(*args, **kwargs)

class PercentageField(models.DecimalField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('max_digits', 5)
        kwargs.setdefault('decimal_places', 2)
        kwargs.setdefault('validators', [number_input_validation])
        super().__init__(*args, **kwargs)

class FinanceBase(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    class Meta:
        abstract = True

class MonthlyFinance(FinanceBase):
    """Monthly financial snapshot"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    month = models.IntegerField()
    year = models.IntegerField()
    
    income = MoneyField(default=0.00)

    # Basic Expenses
    rent_or_mortgage = MoneyField(default=0.00)
    food_grocery = MoneyField(default=0.00)
    utilities = MoneyField(default=0.00)
    transportation = MoneyField(default=0.00)
    insurance = MoneyField(default=0.00)
    healthcare = MoneyField(default=0.00)
    
    # Discretionary Expenses
    entertainment = MoneyField(default=0.00)
    shopping = MoneyField(default=0.00)
    personal_care = MoneyField(default=0.00)

    saving_tax_amount = MoneyField(default=0.00)

    class Meta:
        unique_together = ('user', 'month', 'year')
        indexes = [
            models.Index(fields=['user', 'year', 'month']),
        ] # Add an index for faster querying & optimize sorting
    
class BasicFinance(FinanceBase):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    has_debt = models.BooleanField(null=True, blank=True)
    has_employer_match = models.BooleanField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.month}/{self.year}"

class EmergencyFund(FinanceBase):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    current_amount = MoneyField()
    emergency_fund_target = MoneyField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.username}'s Emergency Fund - ${self.emergency_fund_target}"

class EmergencyFundContribution(FinanceBase):
    """Tracking of emergency fund contributions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    emergency_fund = models.ForeignKey(EmergencyFund, on_delete=models.CASCADE, related_name='contributions')
    contribution_amount = MoneyField()
    contribution_date = models.DateField()
    notes = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.emergency_fund} - {self.contribution_date}"
    
    class Meta:
        ordering = ['-contribution_date']

class Debt(FinanceBase):
    """Defines the debt parameters that remain constant"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=100) # have to create a text input validator for this
    initial_amount = MoneyField()
    interest_rate = PercentageField()
    minimum_payment = MoneyField()
    planned_payment = MoneyField()
    remaining_balance = MoneyField()
    start_date = models.DateField()
    estimated_end_date = models.DateField(null=True, blank=True)
    is_paid_off = models.BooleanField(default=False)

    class Meta:
        unique_together = ('user', 'name')
    
    def __str__(self):
        return f"{self.user.username} - {self.name}"

class MonthlyDebtPayment(FinanceBase):
    """Monthly tracking of debt payments"""
    monthly_finance = models.ForeignKey(MonthlyFinance, on_delete=models.CASCADE)
    debt = models.ForeignKey(Debt, on_delete=models.CASCADE)
    actual_payment = MoneyField(null=True, blank=True)
    interest_paid = MoneyField(default=0.00)
    principal_paid = MoneyField(default=0.00)
    payment_date = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True, null=True)
    
    class Meta:
        unique_together = ('monthly_finance', 'debt')
        ordering = ['-payment_date', 'debt__name']
    
    def save(self, *args, **kwargs):
        # Auto-calculate actual payment if not provided
        if not self.actual_payment and (self.interest_paid or self.principal_paid):
            self.actual_payment = (self.interest_paid or 0) + (self.principal_paid or 0)
        
        # Update debt balance when payment is saved
        if self.pk:  # If updating existing payment
            old_payment = MonthlyDebtPayment.objects.get(pk=self.pk)
            # Reverse old payment effect
            self.debt.remaining_balance += old_payment.principal_paid or 0
        
        # Apply new payment
        if self.principal_paid:
            self.debt.remaining_balance -= self.principal_paid
            if self.debt.remaining_balance <= 0:
                self.debt.remaining_balance = 0
                self.debt.is_paid_off = True
        
        self.debt.save()
        super().save(*args, **kwargs)
    
    def delete(self, *args, **kwargs):
        # Reverse payment effect on debt balance
        if self.principal_paid:
            self.debt.remaining_balance += self.principal_paid
            self.debt.is_paid_off = False
            self.debt.save()
        super().delete(*args, **kwargs)

class EmployerBenefit(FinanceBase):
    ACCOUNT_TYPES = [
        ('group_rrsp', 'Group RRSP'),
        ('pension_plan', 'Public Service Pension Plan'),
        ('cpp_qpp', 'Canada Pension Plan / Quebec Pension Plan'),
        ('other', 'Other'),
    ]
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=100) # have to create a text input validator for this
    employer_match_type = models.CharField(max_length=20, choices=ACCOUNT_TYPES) # have to create a text input validator for this
    employer_match_max = MoneyField(null=True, blank=True)
    employer_match_percent = PercentageField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.name} - {self.month}/{self.year}"
    
class MonthlyEmployerBenefitPayment(FinanceBase):
    """Monthly tracking of employer benefits payments"""
    monthly_finance = models.ForeignKey(MonthlyFinance, on_delete=models.CASCADE)
    employer_benefits = models.ForeignKey(EmployerBenefit, on_delete=models.CASCADE)
    current_contribution = MoneyField(null=True, blank=True)
    current_contribution_percent = PercentageField(null=True, blank=True)

    class Meta:
        unique_together = ('monthly_finance', 'employer_benefits')

class InvestmentAccount(FinanceBase):
    ACCOUNT_TYPES = [
        ('rrsp', 'Registered Retirement Savings Plan'),
        ('tfsa', 'Tax-Free Savings Account'),
        ('fhsa', 'First Home Savings Account'),
        ('non_registered', 'Non-Registered Account'),
        ('lira', 'Locked-In Retirement Account'),
        ('resp', 'Registered Education Savings Plan'),
        ('rdsp', 'Registered Disability Savings Plan'),
        ('crypto', 'Cryptocurrency Account'),
        ('other', 'Other'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPES)
    current_amount = MoneyField()

    def __str__(self):
        return f"{self.user.username} - {self.account_type} - {self.month}/{self.year}"
    
class MonthlyInvestmentAccountPayment(FinanceBase):
    """Monthly tracking of investment account payments"""
    monthly_finance = models.ForeignKey(MonthlyFinance, on_delete=models.CASCADE)
    investment_account = models.ForeignKey(InvestmentAccount, on_delete=models.CASCADE)
    contribution_amount = MoneyField(default=0.00)
    contribution_date = models.DateField(null=True, blank=True)

class Goals(FinanceBase):
    """Defines the saving goal parameters that remain constant"""
    GOAL_CATEGORIES = [
        ('retirement', 'Retirement'),
        ('education', 'Education'),
        ('business', 'Business'),
        ('home', 'Home'),
        ('car', 'Car'),
        ('vacation', 'Vacation'),
        ('wedding', 'Wedding'),
        ('medical', 'Medical'),
        ('large_purchase', 'Large Purchase'),
        ('children_tuition', 'Children Tuition'),
        ('other', 'Other'),
    ] # if a user selects anything from here then show them the idea of from existing page structures that's online

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=100) # have to create a text input validator for this
    target_amount = MoneyField() # should not allow 0
    current_amount = MoneyField()
    monthly_contribution = MoneyField()
    start_date = models.DateField(null=True, blank=True)
    target_date = models.DateField(null=True, blank=True)
    category = models.CharField(max_length=20, choices=GOAL_CATEGORIES)
    is_required = models.BooleanField(default=False)
    is_achieved = models.BooleanField(default=False)

    @property
    def progress_percentage(self):
        return (self.current_amount / self.target_amount) * 100
    
    @property
    def time_remaining(self):
        if self.target_date:
            return self.target_date - self.created_at.date()
        return None
    
    @property
    def monthly_contribution_needed(self):
        if self.time_remaining:
            return (self.target_amount - self.current_amount) / self.time_remaining.days
        return None
    
    @property
    def is_achieved(self):
        return self.current_amount >= self.target_amount

    # retire_early = models.BooleanField(null=True, blank=True)
    # large_purchase_or_personal_investment = models.BooleanField(null=True, blank=True)
    # large_purchase_or_personal_investment_required = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, validators=[number_input_validation])
    # large_purchase_or_personal_investment_time = models.DateField(null=True, blank=True)
    # large_purchase_or_personal_investment_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, validators=[number_input_validation])
    # children_tuition_expenses = models.BooleanField(null=True, blank=True)
    # children_tuition_expenses_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, validators=[number_input_validation])
    # more_immidiate_goals = models.BooleanField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.name} - {self.month}/{self.year}"
    
class MonthlyGoalPayment(FinanceBase):
    """Monthly tracking of goal payments"""
    monthly_finance = models.ForeignKey(MonthlyFinance, on_delete=models.CASCADE)
    goal = models.ForeignKey(Goals, on_delete=models.CASCADE)
    contribution_amount = MoneyField()
    contribution_date = models.DateField(null=True, blank=True)
    
    class Meta:
        unique_together = ('monthly_finance', 'goal')
