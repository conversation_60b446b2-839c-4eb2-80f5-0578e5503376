{% extends 'finance/base_finance.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block finance_content %}
<div class="finance-form-container">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6 text-primary">Debt Management</h1>

        <div class="intro-text mb-6">
            <p class="text-sm opacity-75">
                Track and manage your debts to develop an effective repayment strategy. Add your debts, monitor payments, and work toward financial freedom.
            </p>
        </div>

        <!-- Summary Dashboard -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
                        <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Debt</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">$<span id="total-debt">0.00</span></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Monthly Payments</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">$<span id="monthly-payments">0.00</span></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                        <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Debt-to-Income</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><span id="dti-ratio">0%</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Two Column Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Left Column: Add New Debt -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add New Debt
                </h3>
                
                <form id="debt-form" method="POST" class="space-y-4">
                    {% csrf_token %}
                    <input type="hidden" name="create_debt" value="true">
                    
                    <div class="space-y-4">
                        <div class="form-field">
                            {{ debt_form.name|as_crispy_field }}
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-field">
                                {{ debt_form.initial_amount|as_crispy_field }}
                            </div>
                            <div class="form-field">
                                {{ debt_form.remaining_balance|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-field">
                                {{ debt_form.interest_rate|as_crispy_field }}
                            </div>
                            <div class="form-field">
                                {{ debt_form.minimum_payment|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="form-field">
                            {{ debt_form.planned_payment|as_crispy_field }}
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-field">
                                {{ debt_form.start_date|as_crispy_field }}
                                <script>
                                    document.getElementById('id_start_date').setAttribute('type', 'date');
                                </script>
                            </div>
                            <div class="form-field">
                                {{ debt_form.estimated_end_date|as_crispy_field }}
                                <script>
                                    document.getElementById('id_estimated_end_date').setAttribute('type', 'date');
                                </script>
                            </div>
                        </div>
                        
                        <div class="form-field">
                            {{ debt_form.is_paid_off|as_crispy_field }}
                        </div>
                    </div>
                    
                    <button type="submit" class="w-full btn-save">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Debt
                    </button>
                </form>
            </div>

            <!-- Right Column: Current Debts -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    Current Debts
                </h3>
                
                <div id="debts-container" class="space-y-4 max-h-96 overflow-y-auto">
                    {% for debt in debts %}
                    <div class="debt-item p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-medium text-gray-900 dark:text-white">{{ debt.name }}</h4>
                            <div class="flex space-x-2">
                                <button class="edit-debt text-blue-600 hover:text-blue-800 dark:text-blue-400" data-id="{{ debt.id }}" title="Edit">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button class="delete-debt text-red-600 hover:text-red-800 dark:text-red-400" data-id="{{ debt.id }}" title="Delete">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <div>Balance: <span class="font-medium text-gray-900 dark:text-white">${{ debt.remaining_balance }}</span></div>
                            <div>Rate: <span class="font-medium text-gray-900 dark:text-white">{{ debt.interest_rate }}%</span></div>
                            <div>Min Payment: <span class="font-medium text-gray-900 dark:text-white">${{ debt.minimum_payment }}</span></div>
                            <div>Target: <span class="font-medium text-gray-900 dark:text-white">${{ debt.planned_payment }}</span></div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <p>No debts added yet</p>
                        <p class="text-sm">Add your first debt using the form on the left</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Monthly Payments Section -->
        <div class="mt-8 bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Monthly Payment Tracking - {{ month }}/{{ year }}
            </h3>
            
            <!-- Month/Year Selector -->
            <div class="flex flex-wrap items-center gap-4 mb-6">
                <div class="flex items-center space-x-2">
                    <label for="payment-month-filter" class="text-sm font-medium text-gray-700 dark:text-gray-300">View Month:</label>
                    <input type="month" id="payment-month-filter" class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white rounded-lg px-3 py-2">
                </div>
                <button id="search-payments" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    View Payments
                </button>
                <button id="add-payment-btn" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Payment
                </button>
            </div>

            <!-- Payments Container -->
            <div id="payments-container" class="space-y-4">
                {% for payment in existing_payments %}
                <div class="payment-record p-4 border border-gray-200 dark:border-gray-600 rounded-lg" data-payment-id="{{ payment.id }}">
                    <div class="flex justify-between items-start mb-3">
                        <h4 class="font-medium text-gray-900 dark:text-white">{{ payment.debt.name }}</h4>
                        <div class="flex space-x-2">
                            <button class="edit-payment text-blue-600 hover:text-blue-800 dark:text-blue-400" data-payment-id="{{ payment.id }}" title="Edit Payment">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="delete-payment text-red-600 hover:text-red-800 dark:text-red-400" data-payment-id="{{ payment.id }}" title="Delete Payment">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">Total Payment:</span>
                            <span class="font-medium text-gray-900 dark:text-white ml-1">${{ payment.actual_payment|default:"0.00" }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">Interest:</span>
                            <span class="font-medium text-gray-900 dark:text-white ml-1">${{ payment.interest_paid }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">Principal:</span>
                            <span class="font-medium text-gray-900 dark:text-white ml-1">${{ payment.principal_paid }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">Date:</span>
                            <span class="font-medium text-gray-900 dark:text-white ml-1">{{ payment.payment_date|default:"Not set" }}</span>
                        </div>
                    </div>
                    {% if payment.notes %}
                    <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        <strong>Notes:</strong> {{ payment.notes }}
                    </div>
                    {% endif %}
                </div>
                {% empty %}
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <p>No payments recorded for this month</p>
                    <p class="text-sm">Click "Add Payment" to record your first payment</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="navigation-buttons mt-12 mb-6">
            <button id="previous-button" class="nav-button prev-button">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Emergency Fund
            </button>
            <button id="next-button" class="nav-button next-button">
                Employer Benefits
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        </div>
    </div>
</div>

{% block extra_content %}
{% include 'base/loading_spinner.html' %}
{% include 'base/popup_modal.html' %}
{% endblock extra_content %}
{% endblock %}

{% block finance_js %}
<script src="{% static 'finance/finance.js' %}"></script>
<script src="{% static 'finance/debt_management.js' %}"></script>
{% endblock %}

