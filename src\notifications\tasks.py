from celery import shared_task
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from celery import Celery, states
from celery.exceptions import Ignore
import asyncio
import json

from .models import BroadcastNotification

@shared_task(bind=True)
def broadcast_notification(self, data):
    try:
        notification = BroadcastNotification.objects.filter(id=int(data))
        if len(notification) > 0:
            notification = notification.first()
            channel_layer = get_channel_layer()
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(
                channel_layer.group_send)(
                    "notification_broadcast",
                    {
                        "type": "send_notification",
                        "message": json.dumps(notification.message),
                        # "broadcast_on": notification.broadcast_on.strftime("%Y-%m-%d %H:%M:%S"),
                    },
                )
            notification.sent = True
            notification.save()
            return "Notification sent"
        else:
            self.update_state(state='FAILURE', meta="Notification not found")
            raise Ignore()
    except:
        self.update_state(
            state='FAILURE', 
            meta={
                'exe': "Failed to send notification",
                # 'exc_type': type(ex).__name__,
                # 'exc_message': traceback.format_exc().split("\n"),
            }
        )
        raise Ignore()