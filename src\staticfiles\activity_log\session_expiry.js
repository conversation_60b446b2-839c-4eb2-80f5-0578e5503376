// let sessionCheckInterval;
// let warningTimeout;

// function checkSession() {
//     fetch('/check-session/')
//         .then(response => response.json())
//         .then(data => {
//             if (!data.valid) {
//                 window.location.href = '/accounts/login/';
//             }
//         });
// }

// function extendSession() {
//     fetch('/extend-session/')
//         .then(response => response.json())
//         .then(data => {
//             if (data.success) {
//                 clearTimeout(warningTimeout);
//                 setSessionChecks();
//             }
//         });
// }

// function warnUser() {
//     // Display warning message and option to extend session
//     const warnElement = document.createElement('div');
//     warnElement.innerHTML = `
//         <p>Your session will expire soon. Would you like to extend it?</p>
//         <button onclick="extendSession()">Extend Session</button>
//     `;
//     document.body.appendChild(warnElement);
// }

// function setSessionChecks() {
//     clearInterval(sessionCheckInterval);
//     clearTimeout(warningTimeout);
    
//     sessionCheckInterval = setInterval(checkSession, 60000);  // Check every minute
//     // warningTimeout = setTimeout(warnUser, 1500000);  // Warn after 25 minutes
//     warningTimeout = setTimeout(warnUser, 30000);  // Warn after 25 minutes
// }

// // Start session checks when page loads
// setSessionChecks();

// // Reset session checks on user activity
// document.addEventListener('mousemove', setSessionChecks);
// document.addEventListener('keypress', setSessionChecks);

// // For long-running tasks that don't involve server requests
// function longRunningTask() {
//     // Simulate a long-running task
//     let i = 0;
//     const taskInterval = setInterval(() => {
//         i++;
//         if (i >= 100) {
//             clearInterval(taskInterval);
//         }
//         extendSession();  // Extend session every iteration
//     }, 30000);  // Every 30 seconds
// }