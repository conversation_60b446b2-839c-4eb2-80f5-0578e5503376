// Utility functions
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Modal functions
function showModal(title, content, buttons) {
    const modalHTML = `
        <div id="dynamic-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full mx-4">
                <div class="p-6">
                    <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">${title}</h3>
                    <div class="mb-6">${content}</div>
                    <div class="flex justify-end space-x-3">
                        ${buttons.map((btn, index) => `
                            <button class="btn ${btn.class}" data-action="${index}">${btn.text}</button>
                        `).join('')}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    const modal = document.getElementById('dynamic-modal');
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        } else if (e.target.dataset.action !== undefined) {
            const actionIndex = parseInt(e.target.dataset.action);
            const button = buttons[actionIndex];
            
            if (button.action === 'close') {
                closeModal();
            } else if (typeof button.action === 'function') {
                button.action();
            }
        }
    });
}

function closeModal() {
    const modal = document.getElementById('dynamic-modal');
    if (modal) {
        modal.remove();
    }
}

// Debt Management Class
class DebtManager {
    constructor() {
        this.currentYear = new Date().getFullYear();
        this.currentMonth = new Date().getMonth() + 1;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setCurrentMonth();
        this.loadDebts();
        this.loadSummary();
    }

    setupEventListeners() {
        // Add Payment button
        document.getElementById('add-payment-btn')?.addEventListener('click', () => {
            this.showAddPaymentModal();
        });

        // Search Payments button
        document.getElementById('search-payments')?.addEventListener('click', () => {
            this.searchPayments();
        });

        // Debt actions (edit/delete)
        document.getElementById('debts-container')?.addEventListener('click', (e) => {
            const button = e.target.closest('button');
            if (!button) return;

            const debtId = button.dataset.id;
            
            if (button.classList.contains('edit-debt')) {
                this.editDebt(debtId);
            } else if (button.classList.contains('delete-debt')) {
                this.deleteDebt(debtId);
            }
        });

        // Payment actions (edit/delete)
        document.getElementById('payments-container')?.addEventListener('click', (e) => {
            const button = e.target.closest('button');
            if (!button) return;

            const paymentId = button.dataset.paymentId;
            
            if (button.classList.contains('edit-payment')) {
                this.editPayment(paymentId);
            } else if (button.classList.contains('delete-payment')) {
                this.deletePayment(paymentId);
            }
        });
    }

    setCurrentMonth() {
        const monthFilter = document.getElementById('payment-month-filter');
        if (monthFilter) {
            const now = new Date();
            monthFilter.value = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
        }
    }

    getSelectedMonth() {
        const monthFilter = document.getElementById('payment-month-filter');
        if (monthFilter && monthFilter.value) {
            const [year, month] = monthFilter.value.split('-');
            return { year: parseInt(year), month: parseInt(month) };
        }
        return { year: this.currentYear, month: this.currentMonth };
    }

    async loadDebts() {
        try {
            const response = await fetch('/finance/debt/list/');
            const data = await response.json();
            
            if (data.status === 'success') {
                this.updateDebtsContainer(data.debts);
            }
        } catch (error) {
            console.error('Error loading debts:', error);
            AlertSystem.showError('Failed to load debts');
        }
    }

    async loadSummary() {
        try {
            const response = await fetch('/finance/debt/summary/');
            const data = await response.json();
            
            document.getElementById('total-debt').textContent = data.total_debt.toFixed(2);
            document.getElementById('monthly-payments').textContent = data.monthly_payments.toFixed(2);
            document.getElementById('dti-ratio').textContent = data.dti_ratio;
        } catch (error) {
            console.error('Error loading summary:', error);
        }
    }

    async showAddPaymentModal() {
        try {
            const response = await fetch('/finance/debt/list/');
            const data = await response.json();
            
            if (data.status === 'success' && data.debts.length > 0) {
                const debtOptions = data.debts.map(debt => 
                    `<option value="${debt.id}">${debt.name} (Balance: $${debt.remaining_balance.toFixed(2)})</option>`
                ).join('');
                
                const modalContent = `
                    <form id="add-payment-form" class="space-y-4">
                        <div class="form-field">
                            <label for="debt-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Select Debt <span class="text-red-500">*</span>
                            </label>
                            <select id="debt-select" name="debt_id" class="form-control" required>
                                <option value="">Choose a debt...</option>
                                ${debtOptions}
                            </select>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-field">
                                <label for="interest-paid" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Interest Paid <span class="text-red-500">*</span>
                                </label>
                                <input type="number" id="interest-paid" name="interest_paid" step="0.01" min="0" class="form-control" required>
                            </div>
                            <div class="form-field">
                                <label for="principal-paid" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Principal Paid <span class="text-red-500">*</span>
                                </label>
                                <input type="number" id="principal-paid" name="principal_paid" step="0.01" min="0" class="form-control" required>
                            </div>
                        </div>
                        <div class="form-field">
                            <label for="payment-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Payment Date <span class="text-red-500">*</span>
                            </label>
                            <input type="date" id="payment-date" name="payment_date" class="form-control" required>
                        </div>
                        <div class="form-field">
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Notes (Optional)
                            </label>
                            <textarea id="notes" name="notes" rows="2" class="form-control" placeholder="Optional notes about this payment"></textarea>
                        </div>
                    </form>
                `;
                
                showModal('Add Payment', modalContent, [
                    { text: 'Cancel', class: 'btn-secondary', action: 'close' },
                    { text: 'Add Payment', class: 'btn-primary', action: () => this.submitAddPayment() }
                ]);

                // Set today as default date
                document.getElementById('payment-date').value = new Date().toISOString().split('T')[0];
            } else {
                AlertSystem.showError('No debts available. Please add a debt first.');
            }
        } catch (error) {
            console.error('Error:', error);
            AlertSystem.showError('Failed to load debts.');
        }
    }

    async submitAddPayment() {
        const form = document.getElementById('add-payment-form');
        const formData = new FormData(form);
        
        const { year, month } = this.getSelectedMonth();
        formData.append('year', year);
        formData.append('month', month);
        
        // Debug logging
        console.log('Submitting payment with:', {
            debt_id: formData.get('debt_id'),
            year: formData.get('year'),
            month: formData.get('month'),
            interest_paid: formData.get('interest_paid'),
            principal_paid: formData.get('principal_paid')
        });
        
        try {
            const response = await fetch('/finance/payment/add/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });
            
            const data = await response.json();
            console.log('Response:', data);
            
            if (data.status === 'success') {
                AlertSystem.showSuccess(data.message, () => {
                    closeModal();
                    location.reload();
                });
            } else {
                AlertSystem.showError(data.message || 'Failed to add payment.');
            }
        } catch (error) {
            console.error('Error:', error);
            AlertSystem.showError('Failed to add payment.');
        }
    }

    async searchPayments() {
        const { year, month } = this.getSelectedMonth();
        
        try {
            const response = await fetch(`/finance/payments/${year}/${month}/`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.displaySearchResults(data.payments, data.month_year);
            } else {
                AlertSystem.showError('Failed to load payments for the selected month.');
            }
        } catch (error) {
            console.error('Error:', error);
            AlertSystem.showError('Failed to search payments.');
        }
    }

    displaySearchResults(payments, monthYear) {
        const paymentsContainer = document.getElementById('payments-container');
        
        if (!payments || payments.length === 0) {
            paymentsContainer.innerHTML = `
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <p>No payments found for ${monthYear}</p>
                    <button onclick="location.reload()" class="mt-4 btn-secondary">
                        Back to Current Month
                    </button>
                </div>
            `;
            return;
        }

        const paymentsList = payments.map(payment => `
            <div class="payment-record p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                <div class="flex justify-between items-start mb-3">
                    <h4 class="font-medium text-gray-900 dark:text-white">${payment.debt_name}</h4>
                    <div class="flex space-x-2">
                        <button class="edit-payment text-blue-600 hover:text-blue-800 dark:text-blue-400" data-payment-id="${payment.id}" title="Edit Payment">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                        <button class="delete-payment text-red-600 hover:text-red-800 dark:text-red-400" data-payment-id="${payment.id}" title="Delete Payment">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Total Payment:</span>
                        <span class="font-medium text-gray-900 dark:text-white ml-1">$${payment.actual_payment.toFixed(2)}</span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Interest:</span>
                        <span class="font-medium text-gray-900 dark:text-white ml-1">$${payment.interest_paid.toFixed(2)}</span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Principal:</span>
                        <span class="font-medium text-gray-900 dark:text-white ml-1">$${payment.principal_paid.toFixed(2)}</span>
                    </div>
                </div>
                ${payment.notes ? `<div class="mt-2 text-sm text-gray-600 dark:text-gray-400"><strong>Notes:</strong> ${payment.notes}</div>` : ''}
            </div>
        `).join('');

        paymentsContainer.innerHTML = `
            <div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg">
                <h4 class="font-medium text-blue-900 dark:text-blue-100">Payment History for ${monthYear}</h4>
            </div>
            ${paymentsList}
            <button onclick="location.reload()" class="mt-4 btn-secondary w-full">
                Back to Current Month
            </button>
        `;
    }

    updateDebtsContainer(debts) {
        const debtsContainer = document.getElementById('debts-container');
        
        if (debts.length === 0) {
            debtsContainer.innerHTML = `
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <p>No debts added yet</p>
                    <p class="text-sm">Add your first debt using the form on the left</p>
                </div>
            `;
            return;
        }

        debtsContainer.innerHTML = debts.map(debt => `
            <div class="debt-item p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow">
                <div class="flex justify-between items-start mb-3">
                    <h4 class="font-medium text-gray-900 dark:text-white">${debt.name}</h4>
                    <div class="flex space-x-2">
                        <button class="edit-debt text-blue-600 hover:text-blue-800 dark:text-blue-400" data-id="${debt.id}" title="Edit">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                        <button class="delete-debt text-red-600 hover:text-red-800 dark:text-red-400" data-id="${debt.id}" title="Delete">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <div>Balance: <span class="font-medium text-gray-900 dark:text-white">$${debt.remaining_balance.toFixed(2)}</span></div>
                    <div>Min Payment: <span class="font-medium text-gray-900 dark:text-white">$${debt.minimum_payment ? debt.minimum_payment.toFixed(2) : '0.00'}</span></div>
                </div>
            </div>
        `).join('');
    }

    async editDebt(debtId) {
        // Implementation for editing debt
        console.log('Edit debt:', debtId);
    }

    async deleteDebt(debtId) {
        if (confirm('Are you sure you want to delete this debt? This will also delete all associated payments.')) {
            try {
                const response = await fetch(`/finance/debt/${debtId}/delete/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    AlertSystem.showSuccess(data.message, () => {
                        location.reload();
                    });
                } else {
                    AlertSystem.showError(data.message || 'Failed to delete debt.');
                }
            } catch (error) {
                console.error('Error:', error);
                AlertSystem.showError('Failed to delete debt.');
            }
        }
    }

    async editPayment(paymentId) {
        // Implementation for editing payment
        console.log('Edit payment:', paymentId);
    }

    async deletePayment(paymentId) {
        if (confirm('Are you sure you want to delete this payment? This will affect your debt balance.')) {
            try {
                const response = await fetch(`/finance/payment/${paymentId}/delete/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    AlertSystem.showSuccess(data.message, () => {
                        location.reload();
                    });
                } else {
                    AlertSystem.showError(data.message || 'Failed to delete payment.');
                }
            } catch (error) {
                console.error('Error:', error);
                AlertSystem.showError('Failed to delete payment.');
            }
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new DebtManager();
});
