<aside
  id="drawer-navigation"
  aria-label="Sidenav"
  class="fixed top-0 left-0 z-40 w-64 h-screen pt-14 transition-transform 
         -translate-x-full bg-white border-r border-gray-200 
         md:translate-x-0 dark:bg-gray-800 dark:border-gray-700"
>
  <div class="h-full px-3 py-5 overflow-y-auto bg-white dark:bg-gray-800">

    <!-- Overview -->
    <ul class="space-y-2">
      <li>
        <a
          href="{% url 'home' %}"
          class="flex items-center p-2 text-base font-medium text-gray-900 
                 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group"
        >
          <svg
            aria-hidden="true"
            class="w-6 h-6 text-gray-500 transition duration-75 
                   dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
            <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
          </svg>
          <span class="ml-3">Overview</span>
        </a>
      </li>
    </ul>

    <!-- Finances -->
    <ul class="pt-5 mt-5 space-y-2 border-t border-gray-200 dark:border-gray-700">
      <li>
        <a
          href="{% url 'finance_monthly_overview' %}"
          class="flex items-center p-2 text-base font-medium text-gray-900 
                 rounded-lg transition duration-75 hover:bg-gray-100 
                 dark:hover:bg-gray-700 dark:text-white group"
        >
          <svg
            aria-hidden="true"
            class="flex-shrink-0 w-6 h-6 text-gray-500 transition duration-75 
                   dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <!-- Bars -->
            <rect x="2" y="11" width="3" height="7" rx="0.5"></rect>
            <rect x="7" y="7"  width="3" height="11" rx="0.5"></rect>
            <rect x="12" y="4" width="3" height="14" rx="0.5"></rect>
            <!-- Arrow -->
            <path d="M2.5 13.5 L7.5 8.5 L11.2 12.2 L17 6.4" 
                  fill="none" stroke="currentColor" stroke-width="1.2" 
                  stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M16.1 5.1 L17 6.4 L15.1 7.1" 
                  fill="none" stroke="currentColor" stroke-width="1.2" 
                  stroke-linecap="round" stroke-linejoin="round"></path>
            <!-- Dollar badge -->
            <circle cx="17" cy="3" r="2"></circle>
            <text x="17" y="3.4" text-anchor="middle" font-size="2.6" 
                  font-family="Arial, Helvetica, sans-serif" dominant-baseline="middle">$</text>
          </svg>
          <span class="ml-3">Finances</span>
        </a>
      </li>
    </ul>

    <!-- Investments -->
    <ul class="pt-5 mt-5 space-y-2 border-t border-gray-200 dark:border-gray-700">
      <li>
        <button
          type="button"
          class="flex items-center w-full p-2 text-base font-medium text-gray-900 
                 rounded-lg transition duration-75 group hover:bg-gray-100 
                 dark:text-white dark:hover:bg-gray-700"
          aria-controls="dropdown-investments"
          aria-expanded="false"
          data-collapse-toggle="dropdown-investments"
        >
          <svg
            aria-hidden="true"
            class="flex-shrink-0 w-6 h-6 text-gray-500 transition duration-75 
                   dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <!-- Candlestick 1 -->
            <rect x="3" y="6" width="2" height="8" rx="0.5"></rect>
            <line x1="4" y1="3" x2="4" y2="17" stroke="currentColor" stroke-width="1"></line>
            <!-- Candlestick 2 -->
            <rect x="8" y="4" width="2" height="10" rx="0.5"></rect>
            <line x1="9" y1="2" x2="9" y2="16" stroke="currentColor" stroke-width="1"></line>
            <!-- Candlestick 3 -->
            <rect x="13" y="8" width="2" height="6" rx="0.5"></rect>
            <line x1="14" y1="5" x2="14" y2="15" stroke="currentColor" stroke-width="1"></line>
          </svg>
          <span class="flex-1 ml-3 text-left whitespace-nowrap">Investments</span>
          <svg
            aria-hidden="true"
            class="w-6 h-6"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586 
                 l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 
                 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            ></path>
          </svg>
        </button>
        <ul id="dropdown-investments" class="hidden py-2 space-y-2">
          <li>
            <a
              href="{% url 'transaction_create' %}"
              class="flex items-center p-2 text-base font-medium text-gray-900 
                     rounded-lg transition duration-75 hover:bg-gray-100 
                     dark:hover:bg-gray-700 dark:text-white group"
            >
              <svg
                aria-hidden="true"
                class="flex-shrink-0 w-6 h-6 text-gray-500 transition duration-75 
                       dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="10" cy="10" r="8" fill="none" stroke="currentColor" stroke-width="1.5"></circle>
                <path d="M10 2 A8 8 0 0 1 18 10 H10 Z"></path>
                <line x1="10" y1="10" x2="10" y2="2" stroke="currentColor" stroke-width="1"></line>
                <line x1="10" y1="10" x2="18" y2="10" stroke="currentColor" stroke-width="1"></line>
              </svg>
              <span class="ml-3">Portfolio</span>
            </a>
          </li>
          <li>
            <a
              href="{% url 'transaction_list' %}"
              class="flex items-center p-2 text-base font-medium text-gray-900 
                     rounded-lg transition duration-75 hover:bg-gray-100 
                     dark:hover:bg-gray-700 dark:text-white group"
            >
              <svg
              aria-hidden="true"
              class="flex-shrink-0 w-6 h-6 text-gray-500 transition duration-75 
                    dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
          >
            <!-- Right arrow -->
            <path stroke-linecap="round" stroke-linejoin="round" 
                  d="M7 8h8m0 0l-3-3m3 3l-3 3" />
            <!-- Left arrow -->
            <path stroke-linecap="round" stroke-linejoin="round" 
                  d="M17 16H9m0 0l3 3m-3-3l3-3" />
          </svg>
              <span class="ml-3">Transactions</span>
            </a>
          </li>
        </ul>
      </li>
    </ul>

    <!-- Help -->
    <ul class="pt-5 mt-5 space-y-2 border-t border-gray-200 dark:border-gray-700">
      <li>
        <a
          href="{% url 'contact' %}"
          class="flex items-center p-2 text-base font-medium text-gray-900 
                 rounded-lg transition duration-75 hover:bg-gray-100 
                 dark:hover:bg-gray-700 dark:text-white group"
        >
          <svg
            aria-hidden="true"
            class="flex-shrink-0 w-6 h-6 text-gray-500 transition duration-75 
                   dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 
                 0116 0zm-2 0c0 .993-.241 1.929-.668 
                 2.754l-1.524-1.525a3.997 3.997 0 
                 00.078-2.183l1.562-1.562C15.802 
                 8.249 16 9.1 16 10zm-5.165 
                 3.913l1.58 1.58A5.98 5.98 0 
                 0110 16a5.976 5.976 0 
                 01-2.516-.552l1.562-1.562a4.006 
                 4.006 0 001.789.027zm-4.677-2.796a4.002 
                 4.002 0 01-.041-2.08l-.08.08-1.53-1.533A5.98 
                 5.98 0 004 10c0 .954.223 1.856.619 
                 2.657l1.54-1.54zm1.088-6.45A5.974 
                 5.974 0 0110 4c.954 0 1.856.223 
                 2.657.619l-1.54 1.54a4.002 4.002 
                 0 00-2.346.033L7.246 4.668zM12 
                 10a2 2 0 11-4 0 2 2 0 014 0z"
            ></path>
          </svg>
          <span class="ml-3">Help</span>
        </a>
      </li>
    </ul>

    <!-- Support -->
    <ul class="pt-5 mt-5 space-y-2 border-t border-gray-200 dark:border-gray-700">
      <li>
        <script src="https://storage.ko-fi.com/cdn/widget/Widget_2.js"></script>
        <script>
          kofiwidget2.init('Support on Ko-fi', '#29abe0', 'N4N012QXSW');
          kofiwidget2.draw();
        </script>
      </li>
    </ul>

  </div>

  <!-- Footer -->
  <div
    class="hidden absolute bottom-0 left-0 justify-center w-full p-4 space-x-4 
           bg-white dark:bg-gray-800 lg:flex z-20"
  >
    <p class="text-sm text-gray-500 dark:text-gray-400">
      0.1.0 | &copy; 2024 PennyMize
    </p>
  </div>
</aside>
