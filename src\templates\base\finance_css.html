<style>
    /* Color Variables - Theme Switching */
    :root {
        /* Default Light Theme */
        --text-primary: #1a202c;
        --text-secondary: #4a5568;
        --bg-primary: #ffffff;
        --bg-secondary: #f7fafc;
        --input-bg: #edf2f7;
        --input-border: #e2e8f0;
        --card-bg: #ffffff;
        --card-border: #e2e8f0;
        --income-bg: #ebf8ff;
        --income-border: #90cdf4;
        --income-text: #2b6cb0;
        --expense-bg: #fef2f2;
        --expense-border: #fca5a5;
        --expense-text: #b91c1c;
        --savings-bg: #f0fff4;
        --savings-border: #9ae6b4;
        --savings-text: #276749;
        --section-border: #e2e8f0;
        --button-primary: #4299e1;
        --button-primary-hover: #2b6cb0;
        --button-text: #ffffff;
        --nav-button-bg: #f7fafc;
        --nav-button-border: #e2e8f0;
        --nav-button-text: #1a202c;
        --tab-active-bg: #4299e1;
        --tab-active-text: #ffffff;
        --tab-hover-bg: #e2e8f0;
    }

    .dark {
        /* Dark Theme */
        --text-primary: #f7fafc;
        --text-secondary: #cbd5e0;
        --bg-primary: #1a202c;
        --bg-secondary: #2d3748;
        --input-bg: #2d3748;
        --input-border: #4a5568;
        --card-bg: #2d3748;
        --card-border: #4a5568;
        --income-bg: #1a365d;
        --income-border: #2b6cb0;
        --income-text: #90cdf4;
        --expense-bg: #742a2a;
        --expense-border: #c53030;
        --expense-text: #feb2b2;
        --savings-bg: #1c4532;
        --savings-border: #2f855a;
        --savings-text: #9ae6b4;
        --section-border: #4a5568;
        --button-primary: #3182ce;
        --button-primary-hover: #2c5282;
        --button-text: #ffffff;
        --nav-button-bg: #2d3748;
        --nav-button-border: #4a5568;
        --nav-button-text: #e2e8f0;
        --tab-active-bg: #3182ce;
        --tab-active-text: #ffffff;
        --tab-hover-bg: #4a5568;
    }

    /* Base Styles */
    body {
        color: var(--text-primary);
        background-color: var(--bg-primary);
        transition: background-color 0.3s ease, color 0.3s ease;
    }
    
    /* Finance Page Container */
    .finance-page-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 1.5rem 1rem;
    }
    
    /* Finance Navigation Bar */
    .finance-navbar {
        margin-bottom: 2rem;
        background-color: var(--bg-secondary);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .finance-tabs-container {
        display: flex;
        overflow-x: auto;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        padding: 0.5rem;
    }
    
    .finance-tabs-container::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }
    
    .finance-tab {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.25rem;
        margin-right: 0.25rem;
        color: var(--text-secondary);
        font-size: 0.9rem;
        font-weight: 500;
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.2s ease;
        white-space: nowrap;
        border: 1px solid transparent;
    }
    
    .finance-tab:last-child {
        margin-right: 0;
    }
    
    .finance-tab:hover {
        background-color: var(--tab-hover-bg);
        color: var(--text-primary);
    }
    
    .finance-tab.active {
        background-color: var(--tab-active-bg);
        color: var(--tab-active-text);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .finance-tab .tab-icon {
        width: 18px;
        height: 18px;
        margin-right: 0.5rem;
        flex-shrink: 0;
    }

    .finance-tab .tab-icon svg {
        width: 100%;
        height: 100%;
        display: block;
    }
    
    .finance-tab span {
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    /* Summary Cards */
    .summary-card {
        display: flex;
        align-items: center;
        padding: 1.25rem;
        border-radius: 0.75rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .summary-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .income-card {
        background-color: var(--income-bg);
        border: 1px solid var(--income-border);
    }

    .expense-card {
        background-color: var(--expense-bg);
        border: 1px solid var(--expense-border);
    }

    .savings-card {
        background-color: var(--savings-bg);
        border: 1px solid var(--savings-border);
    }

    .icon-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 0.75rem;
    }

    .income-card .icon-container {
        color: var(--income-text);
    }

    .expense-card .icon-container {
        color: var(--expense-text);
    }

    .savings-card .icon-container {
        color: var(--savings-text);
    }

    .card-content {
        flex: 1;
    }

    .card-label {
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
        color: var(--text-secondary);
    }

    .income-card .card-value {
        color: var(--income-text);
        font-weight: 700;
        font-size: 1.5rem;
    }

    .expense-card .card-value {
        color: var(--expense-text);
        font-weight: 700;
        font-size: 1.5rem;
    }

    .savings-card .card-value {
        color: var(--savings-text);
        font-weight: 700;
        font-size: 1.5rem;
    }

    /* Form Sections */
    .finance-form {
        background-color: var(--bg-secondary);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        padding: 1.5rem 0;
        border-bottom: 1px solid var(--section-border);
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .section-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-primary);
    }

    /* Form Fields */
    .form-field label {
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
    }

    .form-field input {
        background-color: var(--input-bg);
        border: 1px solid var(--input-border);
        color: var(--text-primary);
        border-radius: 0.5rem;
        padding: 0.75rem;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .form-field input:focus {
        border-color: var(--button-primary);
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
        outline: none;
    }

    .form-field select {
        background-color: var(--input-bg);
        border: 1px solid var(--input-border);
        color: var(--text-primary);
        border-radius: 0.5rem;
        padding: 0.75rem;
        appearance: none;
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 1rem;
        padding-right: 2.5rem;
    }

    .field-info {
        display: flex;
        align-items: center;
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    /* Buttons */
    .btn-save {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: var(--button-primary);
        color: var(--button-text);
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s ease, transform 0.1s ease;
    }

    .btn-save:hover {
        background-color: var(--button-primary-hover);
    }

    .btn-save:active {
        transform: translateY(1px);
    }

    /* Navigation */
    .navigation-buttons {
        display: flex;
        justify-content: space-between;
        margin-top: 2rem;
    }

    .nav-button {
        display: inline-flex;
        align-items: center;
        background-color: var(--nav-button-bg);
        border: 1px solid var(--nav-button-border);
        color: var(--nav-button-text);
        border-radius: 0.5rem;
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .nav-button:hover {
        background-color: var(--button-primary);
        border-color: var(--button-primary);
        color: var(--button-text);
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .finance-tab {
            padding: 0.625rem 1rem;
        }
        
        .finance-tab .tab-icon {
            width: 16px;
            height: 16px;
        }
        
        .finance-tab span {
            font-size: 0.8rem;
        }
        
        .navigation-buttons {
            flex-direction: column;
            gap: 1rem;
        }
        
        .nav-button {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .finance-tab {
            padding: 0.5rem 0.625rem;
        }
        
        .finance-tab span {
            display: none;
        }
        
        .finance-tab .tab-icon {
            margin-right: 0;
        }
    }

    /* Remove number input arrows */
    input[type=number]::-webkit-outer-spin-button,
    input[type=number]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    
    input[type=number] {
        -moz-appearance: textfield;
    }

    /* Loading Spinner & Modal */
    .loading-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        color: white;
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .modal-content {
        background-color: var(--card-bg);
        color: var(--text-primary);
        padding: 2rem;
        border-radius: 0.75rem;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }
</style>