from django.db import models
from django.contrib.auth.models import Group, Permission
from django.db.models.signals import post_save
from django.conf import settings

User = settings.AUTH_USER_MODEL # auth.User
ALLOW_CUSTOM_GROUPS = True

SUBSCRIPTION_PERMISSIONS = [
    ("advanced", "Advanced Perm"), # susbcription.advanced to access this permission
    ("pro", "Pro Perm"), # susbcription.pro 
    ("basic", "Basic Perm"), # susbcription.basic
    ("basic_ai", "Basic AI Perm")
]

class Subscription(models.Model):
    name = models.CharField(max_length=120)
    active = models.BooleanField(default=True) # if the subscription is active
    groups = models.ManyToManyField(Group) # one-to-one relationship with the subscription
    permissions = models.ManyToManyField(
        Permission,
        limit_choices_to = {"content_type__app_label": "subscriptions",
                        # "codename__in": ["basic", "basic_ai", "advanced", "pro"]
                        "codename__in": [perm[0] for perm in SUBSCRIPTION_PERMISSIONS],
                        } # only include the permissions that are in the subscriptions app and have the codenames basic, advanced, pro
    )

    def __str__(self):
        return self.name
    
    # custom permissions
    class Meta:
        permissions = SUBSCRIPTION_PERMISSIONS

class UserSubscription(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE) # once the user deleted there subscription will also be deleted
    # the user doesn't have to have a subscription so we set null=True
    subscription = models.ForeignKey(Subscription, on_delete=models.SET_NULL, null=True, blank=True)
    # start_date = models.DateTimeField(auto_now_add=True)
    # end_date = models.DateTimeField(auto_now_add=False, auto_now=False, null=True, blank=True)
    active = models.BooleanField(default=True)

def user_sub_post_save(sender, instance, *args, **kwargs):
    user_sub_instance = instance
    user = user_sub_instance.user
    subscription_obj = user_sub_instance.subscription
    groups_ids = []
    if subscription_obj is not None:
        groups = subscription_obj.groups.all()
        groups_ids = groups.values_list("id", flat=True)
    # user.groups.set(groups)
    if not ALLOW_CUSTOM_GROUPS:
        user.groups.set(groups)
    else:
        subs_qs = Subscription.objects.filter(active=True)
        if subscription_obj is not None:
            subs_qs = subs_qs.exclude(id=subscription_obj.id)
        subs_groups = subs_qs.values_list("groups__id", flat=True) # get the ids of the groups as a list 
        subs_groups_set = set(subs_groups)
        current_groups = user.groups.all().values_list("id", flat=True)
        groups_ids_set = set(groups_ids)
        current_groups_set = set(current_groups) - subs_groups_set
        final_groups__ids = list(groups_ids_set | current_groups_set)
        user.groups.set(final_groups__ids)

post_save.connect(user_sub_post_save, sender=UserSubscription) # when a user subscription is saved, it will trigger the user_sub_post_save function