from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import ListView, TemplateView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Sum, Q, Count, Avg
from django.core.exceptions import ValidationError
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import datetime
from decimal import Decimal
import csv
from .models import (
    Transaction, Brokerage, Currency, Owner, TransactionOwnership, Asset,
    TransactionTypeChoices, Portfolio, Position, DividendIncome, TaxLot,
    PerformanceSnapshot, Alert, CashTransaction, AssetClassChoices
)
from .forms import TransactionForm, AssetForm, TransactionFilterForm

class TransactionListView(LoginRequiredMixin, ListView):
    model = Transaction
    template_name = 'investments/transaction_list.html'
    context_object_name = 'transactions'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        brokerage = self.request.GET.get('brokerage')
        asset_ticker = self.request.GET.get('asset_ticker')
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        owner_name = self.request.GET.get('owner_name')
        asset_class = self.request.GET.get('asset_class')
        action = self.request.GET.get('action')
        currency = self.request.GET.get('currency')

        if brokerage:
            queryset = queryset.filter(brokerage__name__icontains=brokerage)
        if asset_ticker:
            queryset = queryset.filter(asset__ticker__icontains=asset_ticker)
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        if owner_name:
            queryset = queryset.filter(ownerships__owner__name__icontains=owner_name)
        if asset_class:
            queryset = queryset.filter(asset__asset_class__icontains=asset_class)
        if action:
            queryset = queryset.filter(action__icontains=action)
        if currency:
            queryset = queryset.filter(currency__code__icontains=currency)
        

        return queryset.order_by('-date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_step'] = 'transactions'
        context['page_title'] = 'Investment Transactions'

        # Filter options
        context['brokerages'] = Brokerage.objects.filter(is_active=True)
        context['currencies'] = Currency.objects.all()
        context['owners'] = Owner.objects.filter(is_active=True)
        context['asset_classes'] = [(choice.value, choice.label) for choice in AssetClassChoices]
        context['actions'] = [(choice.value, choice.label) for choice in TransactionTypeChoices]

        # Aggregate totals per owner (basic P/L approximation)
        owner_totals = {}
        for transaction in self.get_queryset():
            total_value = transaction.net_amount
            for ownership in transaction.ownerships.all():
                owner_name = ownership.owner.name
                owner_share = total_value * (ownership.percentage / 100)
                owner_totals[owner_name] = owner_totals.get(owner_name, 0) + owner_share

        context['owner_totals'] = owner_totals

        # Summary statistics
        transactions = self.get_queryset()
        context['total_transactions'] = transactions.count()
        context['total_value'] = sum(abs(t.net_amount) for t in transactions)
        context['active_positions'] = Position.objects.filter(
            quantity__gt=0,
            is_active=True
        ).count()

        return context

    def render_to_response(self, context, **response_kwargs):
        # Handle CSV export
        if self.request.GET.get('export') == 'csv':
            return self.export_csv()
        return super().render_to_response(context, **response_kwargs)

    def export_csv(self):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="transactions.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Date', 'Asset', 'Action', 'Quantity', 'Price', 'Total Cost',
            'Fees', 'Brokerage', 'Currency', 'Owners', 'Notes'
        ])

        for transaction in self.get_queryset():
            owners = ', '.join([
                f"{o.owner.name} ({o.percentage}%)"
                for o in transaction.ownerships.all()
            ])
            writer.writerow([
                transaction.date.strftime('%Y-%m-%d'),
                transaction.asset.ticker,
                transaction.get_action_display(),
                transaction.quantity,
                transaction.price,
                transaction.total_cost,
                transaction.fees,
                transaction.brokerage.name,
                transaction.currency.code,
                owners,
                transaction.notes or ''
            ])

        return response

class TransactionCreateView(LoginRequiredMixin, CreateView):
    model = Transaction
    form_class = TransactionForm
    template_name = 'investments/transaction_form.html'
    success_url = reverse_lazy('transaction_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_step'] = 'transactions'
        context['page_title'] = 'Add Transaction'
        context['owners'] = Owner.objects.filter(is_active=True)
        context['action_choices'] = TransactionTypeChoices.choices
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        # Handle ownership splits
        ownership_data = self.request.POST.getlist('owners')
        percentages = self.request.POST.getlist('percentages')

        total_percentage = 0
        for owner_id, percentage in zip(ownership_data, percentages):
            if owner_id and percentage:
                percentage_decimal = Decimal(percentage)
                total_percentage += percentage_decimal
                TransactionOwnership.objects.create(
                    transaction=self.object,
                    owner_id=owner_id,
                    percentage=percentage_decimal
                )

        # Validate total percentage
        if total_percentage != 100 and ownership_data:
            form.add_error(None, "Ownership percentages must sum to 100%.")
            return self.form_invalid(form)

        return response
    
class TransactionUpdateView(LoginRequiredMixin, UpdateView):
    model = Transaction
    form_class = TransactionForm
    template_name = 'investments/transaction_form.html'
    success_url = reverse_lazy('transaction_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['owners'] = Owner.objects.all()
        context['ownerships'] = self.object.ownerships.all()
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        # Update ownerships with validation
        self.object.ownerships.all().delete()
        ownership_data = self.request.POST.getlist('owners')
        percentages = [Decimal(p) for p in self.request.POST.getlist('percentages') if p]
        if sum(percentages) != 100 and ownership_data:
            raise ValidationError("Ownership percentages must sum to 100%.")
        for owner_id, percentage in zip(ownership_data, percentages):
            if owner_id and percentage:
                TransactionOwnership.objects.create(
                    transaction=self.object,
                    owner_id=owner_id,
                    percentage=percentage
                )
        return response

class TransactionDeleteView(LoginRequiredMixin, DeleteView):
    model = Transaction
    template_name = 'investments/transaction_confirm_delete.html'
    success_url = reverse_lazy('transaction_list')

class AssetCreateView(LoginRequiredMixin, CreateView):
    model = Asset
    form_class = AssetForm
    template_name = 'investments/asset_form_modal.html'

    def form_valid(self, form):
        obj = form.save()
        if self.request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({'id': obj.id, 'text': str(obj)})
        return super().form_valid(form)

# Portfolio Views
class PortfolioOverviewView(LoginRequiredMixin, TemplateView):
    template_name = 'investments/portfolio_overview.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_step'] = 'portfolio'
        context['page_title'] = 'Portfolio Overview'

        # Get user's portfolios
        user_owner = Owner.objects.filter(user=self.request.user).first()
        if user_owner:
            portfolios = Portfolio.objects.filter(owner=user_owner, is_active=True)
            context['portfolios'] = portfolios

            # Portfolio summary
            total_value = 0
            total_cost = 0
            for portfolio in portfolios:
                positions = Position.objects.filter(portfolio=portfolio, is_active=True)
                portfolio_value = sum(p.market_value or 0 for p in positions)
                portfolio_cost = sum(p.total_cost_basis for p in positions)
                total_value += portfolio_value
                total_cost += portfolio_cost

            context['total_portfolio_value'] = total_value
            context['total_cost_basis'] = total_cost
            context['total_unrealized_pl'] = total_value - total_cost

        return context

class PositionsListView(LoginRequiredMixin, ListView):
    model = Position
    template_name = 'investments/positions_list.html'
    context_object_name = 'positions'

    def get_queryset(self):
        user_owner = Owner.objects.filter(user=self.request.user).first()
        if user_owner:
            return Position.objects.filter(
                portfolio__owner=user_owner,
                is_active=True,
                quantity__gt=0
            ).select_related('asset', 'brokerage', 'portfolio')
        return Position.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_step'] = 'positions'
        context['page_title'] = 'Current Positions'
        return context

class DividendsListView(LoginRequiredMixin, ListView):
    model = DividendIncome
    template_name = 'investments/dividends_list.html'
    context_object_name = 'dividends'
    paginate_by = 20

    def get_queryset(self):
        return DividendIncome.objects.all().order_by('-date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_step'] = 'dividends'
        context['page_title'] = 'Dividend Income'
        return context

class PerformanceAnalyticsView(LoginRequiredMixin, TemplateView):
    template_name = 'investments/performance_analytics.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_step'] = 'analytics'
        context['page_title'] = 'Performance Analytics'
        return context

class TaxLotsListView(LoginRequiredMixin, ListView):
    model = TaxLot
    template_name = 'investments/tax_lots_list.html'
    context_object_name = 'tax_lots'

    def get_queryset(self):
        user_owner = Owner.objects.filter(user=self.request.user).first()
        if user_owner:
            return TaxLot.objects.filter(
                owner=user_owner,
                remaining_quantity__gt=0
            ).select_related('asset', 'brokerage')
        return TaxLot.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_step'] = 'tax_lots'
        context['page_title'] = 'Tax Lots'
        return context

class AlertsListView(LoginRequiredMixin, ListView):
    model = Alert
    template_name = 'investments/alerts_list.html'
    context_object_name = 'alerts'

    def get_queryset(self):
        user_owner = Owner.objects.filter(user=self.request.user).first()
        if user_owner:
            return Alert.objects.filter(owner=user_owner).order_by('-created_at')
        return Alert.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_step'] = 'alerts'
        context['page_title'] = 'Alerts & Notifications'
        return context