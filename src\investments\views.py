from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import ListView, TemplateView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Sum, Q
from django.core.exceptions import ValidationError
from .models import Transaction, Brokerage, Currency, Owner, TransactionOwnership, Asset
from django.utils import timezone
from datetime import datetime
from decimal import Decimal

class TransactionListView(LoginRequiredMixin, ListView):
    model = Transaction
    template_name = 'investments/transaction_list.html'
    context_object_name = 'transactions'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        brokerage = self.request.GET.get('brokerage')
        asset_ticker = self.request.GET.get('asset_ticker')
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        owner_name = self.request.GET.get('owner_name')
        asset_class = self.request.GET.get('asset_class')
        action = self.request.GET.get('action')
        currency = self.request.GET.get('currency')

        if brokerage:
            queryset = queryset.filter(brokerage__name__icontains=brokerage)
        if asset_ticker:
            queryset = queryset.filter(asset__ticker__icontains=asset_ticker)
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        if owner_name:
            queryset = queryset.filter(ownerships__owner__name__icontains=owner_name)
        if asset_class:
            queryset = queryset.filter(asset__asset_class__icontains=asset_class)
        if action:
            queryset = queryset.filter(action__icontains=action)
        if currency:
            queryset = queryset.filter(currency__code__icontains=currency)
        

        return queryset.order_by('-date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['brokerages'] = Brokerage.objects.all()
        context['currencies'] = Currency.objects.all()
        context['owners'] = Owner.objects.all()
        context['asset_classes'] = Asset.objects.values_list('asset_class', flat=True).distinct()
        context['actions'] = [('BUY', 'Buy'), ('SELL', 'Sell'), ('SHORT', 'Short Sell'), ('COVER', 'Cover Short')]

        # Aggregate totals per owner (basic P/L approximation: quantity * price for now)
        owner_totals = {}
        for transaction in self.get_queryset():
            total_value = transaction.quantity * transaction.price
            for ownership in transaction.ownerships.all():
                owner_totals[ownership.owner.name] = owner_totals.get(ownership.owner.name, 0) + (total_value * (ownership.percentage / 100))
        context['owner_totals'] = owner_totals
        return context

class TransactionCreateView(LoginRequiredMixin, CreateView):
    model = Transaction
    fields = ['brokerage', 'asset', 'date', 'action', 'quantity', 'price', 'fees', 'currency', 'notes', 'is_short']
    template_name = 'investments/transaction_form.html'
    success_url = reverse_lazy('transaction_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['owners'] = Owner.objects.all()
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        # Handle ownership splits (basic form submission)
        ownership_data = self.request.POST.getlist('owners')
        percentages = self.request.POST.getlist('percentages')
        for owner_id, percentage in zip(ownership_data, percentages):
            if owner_id and percentage:
                TransactionOwnership.objects.create(
                    transaction=self.object,
                    owner_id=owner_id,
                    percentage=Decimal(percentage)
                )
        return response
    
class TransactionUpdateView(LoginRequiredMixin, UpdateView):
    model = Transaction
    fields = ['brokerage', 'asset', 'date', 'action', 'quantity', 'price', 'fees', 'currency', 'notes', 'is_short']
    template_name = 'investments/transaction_form.html'
    success_url = reverse_lazy('transaction_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['owners'] = Owner.objects.all()
        context['ownerships'] = self.object.ownerships.all()
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        # Update ownerships with validation
        self.object.ownerships.all().delete()
        ownership_data = self.request.POST.getlist('owners')
        percentages = [Decimal(p) for p in self.request.POST.getlist('percentages') if p]
        if sum(percentages) != 100 and ownership_data:
            raise ValidationError("Ownership percentages must sum to 100%.")
        for owner_id, percentage in zip(ownership_data, percentages):
            if owner_id and percentage:
                TransactionOwnership.objects.create(
                    transaction=self.object,
                    owner_id=owner_id,
                    percentage=percentage
                )
        return response

class TransactionDeleteView(LoginRequiredMixin, DeleteView):
    model = Transaction
    template_name = 'investments/transaction_confirm_delete.html'
    success_url = reverse_lazy('transaction_list')

class AssetCreateView(LoginRequiredMixin, CreateView):
    model = Asset
    fields = ['ticker', 'name', 'asset_class', 'sector', 'currency']
    template_name = 'investments/asset_form_modal.html'  # New template for modal form

    def form_valid(self, form):
        obj = form.save()
        if self.request.headers.get('x-requested-with') == 'XMLHttpRequest':  # AJAX
            return JsonResponse({'id': obj.id, 'text': str(obj)})
        return super().form_valid(form)