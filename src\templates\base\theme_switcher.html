<script>
    // Initialize theme based on localStorage or system preference
    function setTheme() {
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }

    // Initial theme setting
    setTheme();

    // Toggle theme function
    function toggleTheme() {
        if (document.documentElement.classList.contains('dark')) {
            document.documentElement.classList.remove('dark');
            localStorage.theme = 'light';
        } else {
            document.documentElement.classList.add('dark');
            localStorage.theme = 'dark';
        }
    }

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
        if (!('theme' in localStorage)) {
            if (e.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }
    });

    // Make toggle function globally available
    window.toggleTheme = toggleTheme;


    // Update the theme toggle button icons based on current theme
document.addEventListener('DOMContentLoaded', function() {
  const darkIcon = document.getElementById('theme-toggle-dark-icon');
  const lightIcon = document.getElementById('theme-toggle-light-icon');
  
  // Update button icons
  function updateThemeToggleIcons() {
      if (document.documentElement.classList.contains('dark')) {
          darkIcon.classList.add('hidden');
          lightIcon.classList.remove('hidden');
      } else {
          darkIcon.classList.remove('hidden');
          lightIcon.classList.add('hidden');
      }
  }
  
  // Initial icon state
  updateThemeToggleIcons();
  
  // Update icons on theme change
  const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
          if (mutation.attributeName === 'class') {
              updateThemeToggleIcons();
          }
      });
  });
  
  observer.observe(document.documentElement, { attributes: true });
});
</script>