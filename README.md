# PennyMize

A comprehensive web application for tracking personal finances and visualizing spending patterns through intuitive flowcharts and dashboards, with focus on Canadian financial planning.

![PISF Logo](./src/staticfiles/img/logo.png)

## 🌟 Features

- **Modular Financial Management**: Track different aspects of your finances through specialized modules
- **Expense Comparison**: Compare your spending to Canadian provincial averages
- **Interactive Dashboards**: Visualize financial data with Plotly graphs
- **Subscription Tiers**: Different access levels with Stripe payment integration
- **User Authentication**: Secure login, registration, and Google social authentication
- **Personalized Advice**: Financial guidance based on your specific situation
- **Real-time Notifications**: Stay updated with WebSocket-powered notifications
- **Responsive Design**: Modern UI built with TailwindCSS and Flowbite

## 🧩 Core Functionality

### Financial Management Modules
The application offers a modular approach to financial management with specialized sections:

1. **Monthly Overview**: Track income, expenses, and savings on a monthly basis
2. **Emergency Fund**: Set targets and monitor progress towards financial security
3. **Debt Management**: Track and optimize debt repayment strategies
4. **Employer Benefits**: Manage retirement contributions and employment benefits
5. **Investment Accounts**: Track various investment accounts (RRSP, TFSA, etc.)
6. **Financial Goals**: Set and monitor progress towards specific financial objectives

### Data Visualization
- Compare your expense percentages to Canadian provincial averages
- Track monthly/yearly financial changes
- Visualize spending breakdown with interactive charts

## 🚀 Getting Started

### Prerequisites

- Python 3.8+
- Node.js and npm
- PostgreSQL database
- Stripe account for payment processing
- Redis for WebSockets and Celery

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/pisf.git
   cd pisf
   ```

2. Set up a virtual environment:
   ```bash
   python -m venv env
   source env/bin/activate  # On Windows: env\Scripts\activate
   ```

3. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Install Node.js dependencies:
   ```bash
   npm install
   ```

5. Create a `.env` file in the project root and add your configuration:
   ```
   DEBUG=True
   SECRET_KEY=your_secret_key
   DATABASE_URL=your_db_url
   STRIPE_SECRET_KEY=your_stripe_key
   STRIPE_PUBLISHABLE_KEY=your_stripe_pub_key
   EMAIL_HOST_USER=your_email
   EMAIL_HOST_PASSWORD=your_email_password
   REDIS_URL=your_redis_url
   ```

6. Download vendor files for TailwindCSS and Flowbite:
   ```bash
   cd src
   python manage.py vendor_pull
   ```

7. Run migrations:
   ```bash
   python manage.py migrate
   ```

8. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```

9. Build static assets:
   ```bash
   npm run dev
   ```

10. Start the development server:
   ```bash
   python manage.py runserver
   ```

11. For WebSocket and async task support:
   ```bash
   # In separate terminals:
   celery -A cfehome worker --pool=solo -l info
   celery -A cfehome beat -l info
   ```

## 🏗️ Project Structure

- **cfehome/**: Main Django project configuration
- **finance/**: Financial management modules (Monthly Overview, Emergency Fund, Debt Management, etc.)
- **dashboard/**: Visualization and user dashboard interface
- **subscriptions/**: Subscription tiers and payment integration
- **customers/**: Stripe customer integration
- **auth/**: Authentication customization
- **profiles/**: User profile management
- **notifications/**: Real-time WebSocket notifications
- **landing/**: Public landing page and marketing content
- **contact/**: Contact form functionality
- **newsletter/**: Newsletter subscription management
- **activity_log/**: User activity tracking
- **helpers/**: Utility functions and reusable code
- **templates/**: HTML templates organized by app
- **staticfiles/**: CSS, JS, and other static assets

### Finance App Architecture
The finance app follows a modular structure:
- **Models**: Tracks financial data for each category (MonthlyFinance, EmergencyFund, Debt, etc.)
- **Views**: Specialized views for each financial module
- **Templates**: Streamlined interface with consistent navigation
- **CSS/JS**: Responsive design with modern UI components

## 💻 Development

### TailwindCSS Development
```bash
npm run dev
```

### Custom Management Commands

```bash
# Sync subscription permissions and groups
python manage.py sync_subs

# Download vendor static files
python manage.py vendor_pull

# Send test email
python manage.py sendtestemail --admin

# Run tests
python manage.py test
```

### Database Branching for Testing
The project is configured to use Neon DB's database branching for isolated testing environments.

## 🚢 Deployment

The project is configured for deployment on Railway using Docker:

1. Set up required environment variables in Railway
2. Configure the PostgreSQL database connection
3. Ensure Redis is provisioned for WebSockets and Celery
4. Push to your repository with GitHub Actions configured
5. Railway will automatically build and deploy your application

## 🔒 Security Considerations

- Environment variables for sensitive data
- CSRF protection for all forms
- Django's built-in security middleware
- Proper permission checks for all views
- Secure Stripe integration for payments

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgements

- [Django](https://www.djangoproject.com/) - Web framework
- [Django AllAuth](https://django-allauth.readthedocs.io/) - Authentication
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [Flowbite](https://flowbite.com/) - UI components
- [Plotly](https://plotly.com/) - Data visualization
- [Stripe](https://stripe.com/) - Payment processing
- [Celery](https://docs.celeryproject.org/) - Task queue
- [Django Channels](https://channels.readthedocs.io/) - WebSockets
- [Railway](https://railway.app/) - Deployment platform
