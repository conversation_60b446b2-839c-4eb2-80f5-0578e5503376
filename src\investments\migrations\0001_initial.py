# Generated by Django 5.0.2 on 2025-08-23 23:37

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=4, unique=True)),
                ('name', models.CharField(blank=True, max_length=50, null=True)),
                ('symbol', models.CharField(blank=True, max_length=5, null=True)),
            ],
            options={
                'verbose_name_plural': 'Currencies',
            },
        ),
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticker', models.CharField(max_length=20, unique=True)),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('asset_class', models.CharField(blank=True, choices=[('EQUITY', 'Equity (Stocks, ETFs, REITs, ADRs)'), ('FIXED_INCOME', 'Fixed Income (Bonds, CDs, Treasuries)'), ('DERIVATIVE', 'Derivative (Options, Futures)'), ('ALTERNATIVE', 'Alternative (Crypto, Commodities, Private Equity)'), ('CASH', 'Cash & Cash Equivalents'), ('OTHER', 'Other')], max_length=20, null=True)),
                ('asset_type', models.CharField(blank=True, choices=[('STOCK', 'Stock'), ('ETF', 'Exchange Traded Fund'), ('REIT', 'Real Estate Investment Trust'), ('ADR', 'American Depositary Receipt'), ('BOND', 'Bond'), ('CD', 'Certificate of Deposit'), ('TREASURY', 'Treasury Security'), ('OPTION', 'Option'), ('FUTURE', 'Future'), ('WARRANT', 'Warrant'), ('CRYPTO', 'Cryptocurrency'), ('COMMODITY', 'Commodity'), ('PRIVATE_EQUITY', 'Private Equity'), ('CASH', 'Cash'), ('MONEY_MARKET', 'Money Market'), ('OTHER', 'Other')], max_length=20, null=True)),
                ('sector', models.CharField(blank=True, max_length=50, null=True)),
                ('industry', models.CharField(blank=True, max_length=100, null=True)),
                ('country', models.CharField(blank=True, max_length=50, null=True)),
                ('exchange', models.CharField(blank=True, max_length=20, null=True)),
                ('option_type', models.CharField(blank=True, choices=[('CALL', 'Call'), ('PUT', 'Put')], max_length=4, null=True)),
                ('strike_price', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('expiration_date', models.DateField(blank=True, null=True)),
                ('contract_size', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('underlying_asset', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='investments.asset')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='investments.currency')),
            ],
            options={
                'ordering': ['ticker'],
            },
        ),
        migrations.CreateModel(
            name='Brokerage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('account_number', models.CharField(blank=True, max_length=50, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Brokerages',
            },
        ),
        migrations.CreateModel(
            name='DividendIncome',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('amount_per_share', models.DecimalField(decimal_places=6, max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('income_type', models.CharField(choices=[('DIVIDEND', 'Dividend'), ('INTEREST', 'Interest'), ('DISTRIBUTION', 'Distribution'), ('CAPITAL_GAIN', 'Capital Gain Distribution'), ('RETURN_OF_CAPITAL', 'Return of Capital'), ('OTHER', 'Other')], max_length=20)),
                ('is_reinvested', models.BooleanField(default=False)),
                ('tax_withheld', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.asset')),
                ('brokerage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.brokerage')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='investments.currency')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Owner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('tax_id', models.CharField(blank=True, max_length=20, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('default_currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='investments.currency')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='DividendOwnership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('dividend', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ownerships', to='investments.dividendincome')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.owner')),
            ],
        ),
        migrations.CreateModel(
            name='Portfolio',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('default_tax_lot_method', models.CharField(choices=[('FIFO', 'First In, First Out'), ('LIFO', 'Last In, First Out'), ('HIFO', 'Highest In, First Out'), ('SPECIFIC', 'Specific Identification'), ('AVERAGE', 'Average Cost')], default='FIFO', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('base_currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='investments.currency')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.owner')),
            ],
        ),
        migrations.CreateModel(
            name='PerformanceSnapshot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('total_value', models.DecimalField(decimal_places=2, max_digits=20)),
                ('total_cost_basis', models.DecimalField(decimal_places=2, max_digits=20)),
                ('cash_balance', models.DecimalField(decimal_places=2, default=0, max_digits=20)),
                ('total_return', models.DecimalField(decimal_places=2, max_digits=20)),
                ('total_return_percent', models.DecimalField(decimal_places=4, max_digits=10)),
                ('day_change', models.DecimalField(decimal_places=2, default=0, max_digits=20)),
                ('day_change_percent', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('volatility', models.DecimalField(blank=True, decimal_places=6, max_digits=10, null=True)),
                ('sharpe_ratio', models.DecimalField(blank=True, decimal_places=6, max_digits=10, null=True)),
                ('max_drawdown', models.DecimalField(blank=True, decimal_places=4, max_digits=10, null=True)),
                ('benchmark_return', models.DecimalField(blank=True, decimal_places=4, max_digits=10, null=True)),
                ('alpha', models.DecimalField(blank=True, decimal_places=6, max_digits=10, null=True)),
                ('beta', models.DecimalField(blank=True, decimal_places=6, max_digits=10, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('portfolio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_snapshots', to='investments.portfolio')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Alert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('PRICE_ABOVE', 'Price Above'), ('PRICE_BELOW', 'Price Below'), ('PERCENT_GAIN', 'Percentage Gain'), ('PERCENT_LOSS', 'Percentage Loss'), ('PORTFOLIO_VALUE', 'Portfolio Value'), ('DIVIDEND_ANNOUNCEMENT', 'Dividend Announcement'), ('EARNINGS_DATE', 'Earnings Date'), ('OPTION_EXPIRATION', 'Option Expiration'), ('MARGIN_CALL', 'Margin Call'), ('OTHER', 'Other')], max_length=40)),
                ('threshold_value', models.DecimalField(blank=True, decimal_places=6, max_digits=15, null=True)),
                ('threshold_percent', models.DecimalField(blank=True, decimal_places=4, max_digits=10, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_triggered', models.BooleanField(default=False)),
                ('trigger_once', models.BooleanField(default=True)),
                ('email_notification', models.BooleanField(default=True)),
                ('sms_notification', models.BooleanField(default=False)),
                ('push_notification', models.BooleanField(default=True)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('triggered_at', models.DateTimeField(blank=True, null=True)),
                ('last_checked', models.DateTimeField(blank=True, null=True)),
                ('asset', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='investments.asset')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.owner')),
                ('portfolio', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='investments.portfolio')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=6, default=0, max_digits=15)),
                ('average_cost', models.DecimalField(decimal_places=6, default=0, max_digits=15)),
                ('total_cost_basis', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('current_price', models.DecimalField(blank=True, decimal_places=6, max_digits=15, null=True)),
                ('market_value', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('last_price_update', models.DateTimeField(blank=True, null=True)),
                ('unrealized_gain_loss', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('realized_gain_loss', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_dividends', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('is_short', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.asset')),
                ('brokerage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.brokerage')),
                ('portfolio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='positions', to='investments.portfolio')),
            ],
            options={
                'ordering': ['asset__ticker'],
            },
        ),
        migrations.CreateModel(
            name='TaxLot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('purchase_date', models.DateTimeField()),
                ('purchase_price', models.DecimalField(decimal_places=6, max_digits=15)),
                ('original_quantity', models.DecimalField(decimal_places=6, max_digits=15)),
                ('remaining_quantity', models.DecimalField(decimal_places=6, max_digits=15)),
                ('cost_basis_per_share', models.DecimalField(decimal_places=6, max_digits=15)),
                ('total_cost_basis', models.DecimalField(decimal_places=2, max_digits=15)),
                ('adjustment_factor', models.DecimalField(decimal_places=6, default=1.0, max_digits=10)),
                ('is_closed', models.BooleanField(default=False)),
                ('is_short', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.asset')),
                ('brokerage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.brokerage')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.owner')),
            ],
            options={
                'ordering': ['purchase_date'],
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(default=django.utils.timezone.now)),
                ('action', models.CharField(choices=[('BUY', 'Buy'), ('SELL', 'Sell'), ('SHORT', 'Short Sell'), ('COVER', 'Cover Short'), ('DIVIDEND_REINVEST', 'Dividend Reinvestment'), ('STOCK_SPLIT', 'Stock Split'), ('STOCK_DIVIDEND', 'Stock Dividend'), ('SPIN_OFF', 'Spin-off'), ('MERGER', 'Merger'), ('OPTION_EXERCISE', 'Option Exercise'), ('OPTION_ASSIGNMENT', 'Option Assignment'), ('OPTION_EXPIRATION', 'Option Expiration'), ('TRANSFER_IN', 'Transfer In'), ('TRANSFER_OUT', 'Transfer Out')], max_length=20)),
                ('quantity', models.DecimalField(decimal_places=6, max_digits=15)),
                ('price', models.DecimalField(decimal_places=6, max_digits=15)),
                ('fees', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('commission', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('sec_fee', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('other_fees', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('accrued_interest', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('execution_time', models.TimeField(blank=True, null=True)),
                ('order_type', models.CharField(choices=[('MARKET', 'Market'), ('LIMIT', 'Limit'), ('STOP', 'Stop'), ('STOP_LIMIT', 'Stop Limit'), ('OTHER', 'Other')], default='MARKET', max_length=20)),
                ('is_short', models.BooleanField(default=False)),
                ('is_opening', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('trade_reason', models.TextField(blank=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='investments.asset')),
                ('brokerage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.brokerage')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='investments.currency')),
                ('related_transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='investments.transaction')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='TaxLotSale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_sold', models.DecimalField(decimal_places=6, max_digits=15)),
                ('sale_price', models.DecimalField(decimal_places=6, max_digits=15)),
                ('cost_basis_used', models.DecimalField(decimal_places=2, max_digits=15)),
                ('proceeds', models.DecimalField(decimal_places=2, max_digits=15)),
                ('realized_gain_loss', models.DecimalField(decimal_places=2, max_digits=15)),
                ('is_long_term', models.BooleanField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('tax_lot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales', to='investments.taxlot')),
                ('sale_transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tax_lot_sales', to='investments.transaction')),
            ],
        ),
        migrations.AddField(
            model_name='taxlot',
            name='purchase_transaction',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tax_lots_created', to='investments.transaction'),
        ),
        migrations.CreateModel(
            name='CashTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('DEPOSIT', 'Deposit'), ('WITHDRAWAL', 'Withdrawal'), ('TRANSFER_IN', 'Transfer In'), ('TRANSFER_OUT', 'Transfer Out'), ('INTEREST', 'Interest'), ('FEE', 'Fee'), ('DIVIDEND', 'Dividend Payment'), ('OTHER', 'Other')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('date', models.DateTimeField()),
                ('description', models.CharField(blank=True, max_length=200, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('brokerage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.brokerage')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='investments.currency')),
                ('related_dividend', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='investments.dividendincome')),
                ('portfolio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cash_transactions', to='investments.portfolio')),
                ('related_transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='investments.transaction')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='TransactionOwnership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.owner')),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ownerships', to='investments.transaction')),
            ],
        ),
        migrations.CreateModel(
            name='CurrencyConversionRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rate', models.DecimalField(decimal_places=8, max_digits=15)),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('source', models.CharField(default='manual', max_length=50)),
                ('from_currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='from_rates', to='investments.currency')),
                ('to_currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='to_rates', to='investments.currency')),
            ],
            options={
                'unique_together': {('from_currency', 'to_currency', 'date')},
            },
        ),
        migrations.AddConstraint(
            model_name='dividendownership',
            constraint=models.CheckConstraint(check=models.Q(('percentage__gte', 0), ('percentage__lte', 100)), name='dividend_percentage_range'),
        ),
        migrations.AlterUniqueTogether(
            name='dividendownership',
            unique_together={('dividend', 'owner')},
        ),
        migrations.AlterUniqueTogether(
            name='portfolio',
            unique_together={('owner', 'name')},
        ),
        migrations.AlterUniqueTogether(
            name='performancesnapshot',
            unique_together={('portfolio', 'date')},
        ),
        migrations.AlterUniqueTogether(
            name='position',
            unique_together={('portfolio', 'asset', 'brokerage')},
        ),
        migrations.AddConstraint(
            model_name='transactionownership',
            constraint=models.CheckConstraint(check=models.Q(('percentage__gte', 0), ('percentage__lte', 100)), name='transaction_percentage_range'),
        ),
        migrations.AlterUniqueTogether(
            name='transactionownership',
            unique_together={('transaction', 'owner')},
        ),
    ]
