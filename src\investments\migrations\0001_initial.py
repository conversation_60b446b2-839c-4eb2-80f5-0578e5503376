# Generated by Django 5.0.2 on 2025-08-16 17:42

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=3, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Brokerage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticker', models.CharField(max_length=20, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('asset_class', models.CharField(choices=[('EQUITY', 'Equity (Stocks, ETFs, REITs, ADRs)'), ('FIXED_INCOME', 'Fixed Income (Bonds, CDs, Treasuries)'), ('DERIVATIVE', 'Derivative (Options, Futures)'), ('ALTERNATIVE', 'Alternative (Crypto, Commodities, Private Equity)'), ('OTHER', 'Other')], max_length=20)),
                ('sector', models.CharField(blank=True, max_length=50, null=True)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='investments.currency')),
            ],
        ),
        migrations.CreateModel(
            name='Owner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(default=django.utils.timezone.now)),
                ('action', models.CharField(choices=[('BUY', 'Buy'), ('SELL', 'Sell'), ('SHORT', 'Short Sell'), ('COVER', 'Cover Short')], max_length=10)),
                ('quantity', models.DecimalField(decimal_places=4, max_digits=15)),
                ('price', models.DecimalField(decimal_places=4, max_digits=15)),
                ('fees', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_short', models.BooleanField(default=False)),
                ('rolling_quantity', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('rolling_cost_basis', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='investments.asset')),
                ('brokerage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.brokerage')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='investments.currency')),
            ],
        ),
        migrations.CreateModel(
            name='TransactionOwnership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='investments.owner')),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ownerships', to='investments.transaction')),
            ],
        ),
        migrations.CreateModel(
            name='CurrencyConversionRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rate', models.DecimalField(decimal_places=6, max_digits=10)),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('from_currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='from_rates', to='investments.currency')),
                ('to_currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='to_rates', to='investments.currency')),
            ],
            options={
                'unique_together': {('from_currency', 'to_currency', 'date')},
            },
        ),
        migrations.AddConstraint(
            model_name='transactionownership',
            constraint=models.CheckConstraint(check=models.Q(('percentage__gte', 0), ('percentage__lte', 100)), name='percentage_range'),
        ),
        migrations.AlterUniqueTogether(
            name='transactionownership',
            unique_together={('transaction', 'owner')},
        ),
    ]
