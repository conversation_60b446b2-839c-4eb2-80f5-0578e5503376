{% extends 'dashboard/base.html' %}
{% load crispy_forms_tags %}
{% load static %}

{% block head_title %}{{ page_title }} - {{ block.super }}{% endblock head_title %}

{% block content %}
<div class="finance-page-container">
    <!-- Finance Navigation Tabs -->
    <div class="finance-navbar">
        <div class="finance-tabs-container">
            <a href="{% url 'finance_monthly_overview' %}" class="finance-tab {% if active_step == 'monthly' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                    <line x1="16" y1="2" x2="16" y2="6" />
                    <line x1="8" y1="2" x2="8" y2="6" />
                    <line x1="3" y1="10" x2="21" y2="10" />
                  </svg>                  
                <span>Monthly Overview</span>
            </a>
            
            <a href="{% url 'finance_emergency_fund' %}" class="finance-tab {% if active_step == 'emergency_fund' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <path d="M12 2l8 4v6c0 5.25-3.6 9.33-8 10-4.4-.67-8-4.75-8-10V6l8-4z" />
                    <path d="M12 8v4" />
                    <circle cx="12" cy="16" r="1" />
                  </svg>                  
                <span>Emergency Fund</span>
            </a>
            
            <a href="{% url 'finance_debt_management' %}" class="finance-tab {% if active_step == 'debt' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <rect x="2" y="6" width="20" height="12" rx="2" />
                    <line x1="2" y1="10" x2="22" y2="10" />
                    <circle cx="16" cy="16" r="1.5" />
                  </svg>                  
                <span>Debt Management</span>
            </a>
            
            <a href="{% url 'finance_employer_benefits' %}" class="finance-tab {% if active_step == 'benefits' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <path d="M4 7h16v10H4z" />
                    <path d="M9 7V4h6v3" />
                    <circle cx="12" cy="12" r="1" />
                  </svg>                  
                <span>Employer Benefits</span>
            </a>
            
            <a href="{% url 'finance_investment_accounts' %}" class="finance-tab {% if active_step == 'investments' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <polyline points="23 6 13.5 15.5 8.5 10.5 1 18" />
                    <polyline points="17 6 23 6 23 12" />
                </svg>
                <span>Investment Accounts</span>
            </a>
            
            <a href="{% url 'finance_financial_goals' %}" class="finance-tab {% if active_step == 'goals' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <circle cx="12" cy="12" r="10" />
                    <circle cx="12" cy="12" r="6" />
                    <circle cx="12" cy="12" r="2" />
                  </svg>
                <span>Financial Goals</span>
            </a>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="finance-content">
        {% block finance_content %}{% endblock %}
    </div>

    {% block extra_content %}
    {% include 'base/loading_spinner.html' %}
    {% include 'base/popup_modal.html' %}
    {% endblock extra_content %}
</div>
{% endblock content %}

{% block extra_js %}
<script src="{% static 'finance/finance.js' %}"></script>
{% block finance_js %}{% endblock %}
<script>
    // Add horizontal scrolling with mousewheel for the tabs
    document.addEventListener('DOMContentLoaded', function() {
        const tabsContainer = document.querySelector('.finance-tabs-container');
        if (tabsContainer) {
            tabsContainer.addEventListener('wheel', function(e) {
                if (e.deltaY !== 0) {
                    e.preventDefault();
                    tabsContainer.scrollLeft += e.deltaY;
                }
            });
        }
    });
</script>
{% endblock extra_js %}