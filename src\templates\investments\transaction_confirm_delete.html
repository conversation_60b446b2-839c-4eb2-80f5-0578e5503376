{% extends "dashboard/base.html" %}
{% load static %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-4">Delete Transaction</h1>
    <p>Are you sure you want to delete the transaction for {{ object.asset.ticker }} on {{ object.date|date:"Y-m-d" }}?</p>
    <form method="post">
        {% csrf_token %}
        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700">Yes, Delete</button>
        <a href="{% url 'transaction_list' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">Cancel</a>
    </form>
</div>
{% endblock %}