{% extends 'investments/base_investments.html' %}
{% load static %}

{% block investments_content %}
<div class="finance-form-container">
    <div class="max-w-2xl mx-auto">
        <div class="text-center mb-8">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Delete Transaction</h1>
            <p class="text-gray-600">This action cannot be undone. Please confirm you want to delete this transaction.</p>
        </div>

        <!-- Transaction Details -->
        <div class="form-section">
            <h2 class="section-title">Transaction Details</h2>
            <div class="bg-gray-50 p-6 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Asset</span>
                        <p class="text-lg font-semibold">{{ object.asset.ticker }} - {{ object.asset.name }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Action</span>
                        <p class="text-lg">
                            <span class="action-badge action-{{ object.action|lower }}">
                                {{ object.get_action_display }}
                            </span>
                        </p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Quantity</span>
                        <p class="text-lg">{{ object.quantity|floatformat:4 }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Price</span>
                        <p class="text-lg">${{ object.price|floatformat:2 }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Date</span>
                        <p class="text-lg">{{ object.date|date:"M d, Y" }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Brokerage</span>
                        <p class="text-lg">{{ object.brokerage.name }}</p>
                    </div>
                </div>

                {% if object.ownerships.all %}
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <span class="text-sm font-medium text-gray-500">Ownership</span>
                    <div class="mt-2">
                        {% for ownership in object.ownerships.all %}
                            <span class="inline-block bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded mr-2">
                                {{ ownership.owner.name }} ({{ ownership.percentage }}%)
                            </span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Confirmation Form -->
        <form method="post" class="form-section">
            {% csrf_token %}
            <div class="form-actions">
                <button type="submit" class="btn-delete">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Yes, Delete Transaction
                </button>

                <a href="{% url 'transaction_list' %}" class="btn-cancel">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock investments_content %}