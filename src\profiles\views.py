from django.contrib.auth.decorators import login_required
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse
from django.contrib.auth import get_user_model

User = get_user_model() # getting all the user data

@login_required
def profile_details_view(request, username=None, *args, **kwargs):
    user = request.user # this is the user that is logged in
    # print("user.has_perm('auth.view_user')", user.has_perm("auth.view_user"))
    # print("visits.has_perm('visits.view_pagevisit')", user.has_perm("visits.view_pagevisit"))
    
    # <app_label>.view_<model_name>
    # <app_label>.add_<model_name>
    # <app_label>.change_<model_name>
    # <app_label>.delete_<model_name>

    # user_groups = user.groups.all() # get all the groups that the user is in
    # print("user_groups", user_groups)
    # if user_groups.filter(name__icontains="basic").exists():
    #     return HttpResponse("You have basic access")

    print(
        user.has_perm("subscriptions.basic"),
        user.has_perm("subscriptions.basic_ai"),
        user.has_perm("subscriptions.pro"),
        user.has_perm("subscriptions.advanced"),
    )

    # profile_user_obj = User.objects.get(username=username)
    profile_user_obj = get_object_or_404(User, username=username) # better way if no user exists then return 404
    is_me = profile_user_obj == user
    context = {
        "object": profile_user_obj,
        "instance": profile_user_obj,
        "owner": is_me,
    }
    # return HttpResponse(f"<h1>Profile Page</h1><h2>{username} - {profile_user_obj.id} - {user.id} - {is_me}</h2>")
    return render(request, "profiles/details.html", context)

@login_required
def profile_list_view(request):
    context = {
        "object_list": User.objects.filter(is_active=True)
    }
    return render(request, "profiles/user_list.html", context)