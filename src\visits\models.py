from django.db import models

# Create your models here.
class PageVisit(models.Model):
    # db -> table
    # id -> primary key -> hidden -> autofield -> auto increment -> 1,2,3,4,5
    path = models.TextField(blank=True, null=True) # col
    # this will store the time when the record was created in this case when the page was visited
    timestamp = models.DateTimeField(auto_now_add=True) # col

    def __str__(self):
        return f"{self.path} - {self.timestamp}"