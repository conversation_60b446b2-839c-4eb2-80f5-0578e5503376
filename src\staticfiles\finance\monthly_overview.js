FinanceForm.MonthlyOverview = (function() {
    const form = document.getElementById('finance-form');
    const loadingIndicator = document.getElementById('loading-indicator');
    const messageElement = document.getElementById('data-message');
    let objectId = null;

    function initMonthlyOverview() {
        console.log('Initializing monthly overview');
        
        // Initialize common functionality from finance.js
        if (typeof FinanceForm.common !== 'undefined') {
            FinanceForm.common.init();
        }

        // Handle month/year selection change
        ['id_month', 'id_year'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                console.log('Adding change listener to:', id);
                element.addEventListener('change', () => {
                    const month = document.getElementById('id_month').value;
                    const year = document.getElementById('id_year').value;
                    console.log('Month/Year changed:', month, year);
                    if (month && year) {
                        fetchAndPopulateData(year, month);
                    }
                });
            } else {
                console.warn('Element not found:', id);
            }
        });

        // Handle form submission
        if (form) {
            form.addEventListener('submit', customValidationAndSubmit);
        } else {
            console.warn('Form element not found');
        }

        // Update summary on initial load with current form values
        const initialFormData = {
            income: document.getElementById('id_income').value,
            rent_or_mortgage: document.getElementById('id_rent_or_mortgage').value,
            food_grocery: document.getElementById('id_food_grocery').value,
            utilities: document.getElementById('id_utilities').value,
            transportation: document.getElementById('id_transportation').value,
            insurance: document.getElementById('id_insurance').value,
            healthcare: document.getElementById('id_healthcare').value,
            entertainment: document.getElementById('id_entertainment').value,
            shopping: document.getElementById('id_shopping').value,
            personal_care: document.getElementById('id_personal_care').value,
            saving_tax_amount: document.getElementById('id_saving_tax_amount').value
        };
        
        updateSummary(initialFormData);
    }

    function resetFormAndErrors() {
        console.log('Resetting form and errors');
        
        form.reset();
        
        document.querySelectorAll("#finance-form input").forEach(input => {
            if (input.type !== "hidden" && input.type !== "submit" ) { 
                console.log('Clearing field:', input.id);
                input.value = "";
            }
        });
        document.querySelectorAll('.invalid-feedback, .error-message').forEach(el => el.remove());
        document.querySelectorAll('.is-invalid').forEach(field => field.classList.remove('is-invalid'));
    }

    function showLoadingIndicator() {
        loadingIndicator.style.display = 'flex';
    }

    function hideLoadingIndicator() {
        loadingIndicator.style.display = 'none';
    }

    function calculateTotalExpenses(data) {
        const expenseFields = [
            'rent_or_mortgage',
            'food_grocery',
            'utilities',
            'transportation',
            'insurance',
            'healthcare',
            'entertainment',
            'shopping',
            'personal_care'
        ];
        
        return expenseFields.reduce((total, field) => {
            const value = parseFloat(data[field]) || 0;
            return total + value;
        }, 0);
    }

    function updateSummary(data) {
        const totalIncome = parseFloat(data.income) || 0;
        const totalExpenses = calculateTotalExpenses(data);
        const totalSavings = parseFloat(data.saving_tax_amount) || 0;

        // Safely update elements if they exist
        const updateElement = (id, value) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = `$${value.toFixed(2)}`;
            }
        };

        updateElement('total-income', totalIncome);
        updateElement('total-expenses', totalExpenses);
        updateElement('total-savings', totalSavings);

        // Optional: Log summary for debugging
        console.log('Summary Updated:', {
            income: totalIncome,
            expenses: totalExpenses,
            savings: totalSavings
        });
    }

    function fetchAndPopulateData(year, month) {
        console.log('Fetching data for:', year, month);
        showLoadingIndicator();
        fetch(`/finance/monthly-overview/?year=${year}&month=${month}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(data => {
                console.log('Received data:', data);
                try {
                    resetFormAndErrors();
                    document.getElementById('id_month').value = month;
                    document.getElementById('id_year').value = year;
                    hideLoadingIndicator();
                    
                    // Make sure messageElement has an ID for reference in other functions
                    messageElement.id = 'message-container';
                    
                    if (data.info) {
                        // No data exists for this month/year
                        console.log('No data for selected month/year');
                        messageElement.innerHTML = `
                            <div class="flex items-center p-4 mb-4 text-sm text-blue-800 border border-blue-300 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:border-blue-800" role="alert">
                                <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                                </svg>
                                <span class="sr-only">Info</span>
                                <div>
                                    <span class="font-medium">${data.info}</span> If you want to fill the form for the selected month and year, please proceed.
                                </div>
                            </div>
                        `;
                        
                        // Reset form modified flag since we're starting with a clean slate
                        FinanceForm.common.resetFormModified();
                        return;
                    }

                    console.log('Populating form with data');
                    objectId = data.object_id;
                    Object.entries(data).forEach(([key, value]) => {
                        const field = document.getElementById(`id_${key}`);
                        if (field) {
                            console.log('Setting field:', key, value);
                            field.value = value;
                        }
                    });

                    messageElement.innerHTML = `
                        <div class="flex items-center p-4 mb-4 text-sm text-yellow-800 border border-yellow-300 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300 dark:border-yellow-800" role="alert">
                            <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                            </svg>
                            <span class="sr-only">Info</span>
                            <div>
                                <span class="font-medium">You've already filled the data for this month.</span> Fields are populated with existing data. Please modify if you wish to change.
                            </div>
                        </div>
                    `;

                    updateSummary(data);
                    FinanceForm.common.resetFormModified();
                } catch (error) {
                    console.error('Error processing data:', error);
                    hideLoadingIndicator();
                    messageElement.innerHTML = `
                        <div class="alert alert-danger" role="alert">
                            Error processing data 1: ${error.message}
                        </div>
             
                        
                        `;
                }
            })
            .catch(error => {
                console.error('Error in fetchAndPopulateData:', error);
                hideLoadingIndicator();
                AlertSystem.showError(`Error fetching data: ${error.message}`);
            });
    }

    function customValidationAndSubmit(event) {
        event.preventDefault();
        const income = parseFloat(document.getElementById('id_income').value) || 0;
        const saving_tax_amount = parseFloat(document.getElementById('id_saving_tax_amount').value) || 0;

        // Only validate if both values are provided and not zero
        if (saving_tax_amount > 0 && income > 0 && saving_tax_amount >= income) {
            AlertSystem.showError('Saving tax amount cannot be greater than or equal to income.');
            return;
        }

        showLoadingIndicator();
        const formData = new FormData(form);
        if (objectId) {
            formData.append('object_id', objectId);
        }

        // Set default values for empty optional fields
        const optionalFields = ['transportation', 'insurance', 'healthcare', 'entertainment', 
                            'shopping', 'personal_care', 'saving_tax_amount'];
        
        optionalFields.forEach(field => {
            const input = document.getElementById(`id_${field}`);
            if (input && (input.value === '' || input.value === null)) {
                formData.set(field, '0.00');
            }
        });

        fetch(window.location.pathname, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(data => {
                hideLoadingIndicator();
                if (data.status === 'success') {
                    AlertSystem.showSuccess('Data saved successfully', () => {
                        // Update summary with form data since we know it's valid
                        const formData = {
                            income: document.getElementById('id_income').value,
                            rent_or_mortgage: document.getElementById('id_rent_or_mortgage').value,
                            food_grocery: document.getElementById('id_food_grocery').value,
                            utilities: document.getElementById('id_utilities').value,
                            transportation: document.getElementById('id_transportation').value,
                            insurance: document.getElementById('id_insurance').value,
                            healthcare: document.getElementById('id_healthcare').value,
                            entertainment: document.getElementById('id_entertainment').value,
                            shopping: document.getElementById('id_shopping').value,
                            personal_care: document.getElementById('id_personal_care').value,
                            saving_tax_amount: document.getElementById('id_saving_tax_amount').value
                        };
                        
                        updateSummary(formData);
                        objectId = data.object_id || objectId;
                        FinanceForm.common.resetFormModified();
                        
                        // Make sure messageElement has an ID for reference in other functions
                        messageElement.id = 'message-container';
                        
                        // Update the message to show that data exists for this month/year
                        messageElement.innerHTML = `
                            <div class="flex items-center p-4 mb-4 text-sm text-yellow-800 border border-yellow-300 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300 dark:border-yellow-800" role="alert">
                                <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                                </svg>
                                <span class="sr-only">Info</span>
                                <div>
                                    <span class="font-medium">You've filled the data for this month.</span> Fields are populated with your data. Please modify if you wish to change.
                                </div>
                            </div>
                        `;
                    });
                } else if (data.errors) {
                    // Handle validation errors
                    console.error('Validation errors:', data.errors);
                    let errorMessage = 'Please correct the following errors:<br>';
                    Object.entries(data.errors).forEach(([field, errors]) => {
                        errorMessage += `- ${field}: ${errors.join(', ')}<br>`;
                    });
                    AlertSystem.showError(errorMessage);
                } else {
                    AlertSystem.showError('Unknown error occurred while saving data');
                }
            })
            .catch(error => {
                hideLoadingIndicator();
                console.error('Error in form submission:', error);
                
                // Try to parse error message if it's JSON
                let errorMsg = error.message;
                try {
                    if (error.message.startsWith('{')) {
                        const errorData = JSON.parse(error.message);
                        errorMsg = '';
                        Object.entries(errorData).forEach(([field, errors]) => {
                            if (Array.isArray(errors)) {
                                errorMsg += `${field}: ${errors.join(', ')}<br>`;
                            } else {
                                errorMsg += `${field}: ${errors}<br>`;
                            }
                        });
                    }
                } catch (e) {
                    // If parsing fails, use the original error message
                    console.error('Error parsing error message:', e);
                }
                
                AlertSystem.showError(`Error saving data: ${errorMsg}`);
            });
    }

    return {
        init: initMonthlyOverview,
        customValidationAndSubmit: customValidationAndSubmit,
        fetchAndPopulateData: fetchAndPopulateData
    };
})();

document.addEventListener("DOMContentLoaded", FinanceForm.MonthlyOverview.init);
