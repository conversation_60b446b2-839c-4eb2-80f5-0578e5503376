# pisf
Personal income spending flowchart web app  

After requirements are done.   
Start by creating new django project.

## project with backend

```
django-admin startproject backend
```
Now cd into backend directory and create a new django app. Apps are actually when we create our own custom views and custom code.  
If you see inside the backend directory we've another directory named the same. This is where we have the settings and we link different applications. But here we don't write our custom django code.  
We write our custom django code in an app and we can have multiple different apps for different types of things we'll be doing. Maybe for logic out or for authentication for a certain component of our applicaton.  
But in this case we just need a single app
```
python manage.py startapp api
```
That will make a new directory for us called api.  
Now we have to go the inner backend directory and change some settings.

Now put the requirements.txt in parent backend directory.

JWT (Json Web Token)

JWT will act as the permissions or authentication everytime we access a website.

The idea is we've a frontend and frontend is separate from our backend. So, everytime we requests to the backend the backend needs to know who we are what are the permissions we have to do.

So we'll include a token, a long with our requestt to the backend that token can be decoded to understand a certain set of permission.

#######

Now go to the api directory and create `serializer.py`

After that go to `views.py` to crate the views.

It's time to add create the urls in sub backend `urls.py`

whenever you change datamodel we have to run migrations.

run

```
python manage.py makemigrations
python manage.py migrate
```
to run our application

```
python manage.py runserver
```
###### Note Model

It's time to create a new model for note.
First set the model in `models.py` under api directory. Then implement serializer for the note model in `serializer.py`

Now we gonna create some views to creating a note and deleting a note.

Now make a new file `urls.py` inside the api directory. Now we need to link the urls from the main urls file to this file.

run

```
python manage.py makemigrations
python manage.py migrate
```

## project with src

create the folder src then go to the folder with command line

create the project

```
django-admin startproject cfehome .
```
the dot at the end means create the project in src without creating another main folder with the project name.

Then create the file `views.py`

To up and run with the templates engine create templates folder and assign the directory in settings.py

Now whatever template you add as a html page and create a view for that in view.py you have to add urls for those views in urls.py as well.

### inheritence
to inherit from other template html file
```
{% extends 'base.html' %} # this line will simply render base.html file 
```

To include something from other templates without inheriting
```
{% include 'snippets/welcome-user-msg.html' %}
```

Templates engine is useful because:
- We can include other templates - we can make those templates really really small.
- We also have the ability to extend from another template to inherit what they have going on except for the part that we want to replace.

### save & retrive data
To save and retrive data we use django app more specifically django ORM through models.
Keep your app as small as possible to serve a specific thing if you really need something inside that app then add those otherwise create a new app.

Now let's create an app to count the website visitor. So we'll see which page has been visited and at what time it has been visited.

First go to the models.py and create the database.

Whenever you make changes to the models.py make sure you run the command below

```
python manage.py makemigrations
```
It's just saying get ready to change the database. This is like git because we can track these changes over time.

Then we have to run the following command to apply those changes that we staged in the previous step.

```
python manage.py migrate
```

## production
To make sure the app is working in DEBUG mode in production stage we gotta add ALLOWED_HOSTS in settings.

after that we have to create the dockerfile, railway.toml and the requirements.txt
railway.toml is used so that we can manage different dockerfile.
railway will trigger a build whenever any file changes that's mentioned in watchPatterns in railway.toml

once we make the code production ready and deploy we will be seeing that our local database is not available in the deployed app.

#### Manage environment variables
To manage environment variables we will be using dotenv and python decouple.

If you want to create new Django secret key per month you can do so but it invalidate a couple of things like sessions - [post](https://www.codingforentrepreneurs.com/blog/create-a-one-off-django-secret-key/)

After that add this variable to railway.

## production database

#### Provision a Serverless PostgreSQL Database with Neon

We will use Neon Postgres. After creating the database on the site. We'll copy the connection string and paste it .env file.
Then add the Database url from Neon to the .env file and add that to railway as well. also add it to settings.py using decouple.

#### integrate Postgres Database with Django

We will use psycopg-binary package to do that. This is also useful in production environment.
After done with the initial settings we'll have to run the migrate command.

Now run the app in local server and refresh the page of visit app both the page home and about.
You will notice now changes sync accorss local and production as we are using Neon postgress in both site.

#### Neon in Production + Database Branching

Now create branch and change the database url variable in .env and refresh the page in local setup you will notice that though it will start from same visit numbers but the changes of numbers wont reflect to the production environment.

## Styling with CDN for TailwindCSS and Flowbite 

We will be using flowbite which is built on top of TailwindCSS so that we don't write things from scratch.
Maybe in long term we won't use other's cdn for vulnerability issue.

## Configure Django Static Files in Dev

Now we will configure django to manage our static files. This will set the foundation of what we will do in the future with our own content delivery network or more robust ways that manage all kinds of static files including file uploads.
Another main reason for using our own CDN is maybe we want to use our own css files in future.
Now I will create a folder called statifiles inside the src directory to source static file that django manages it's not gonna be the destination. 

So after downloading the css and js under staticfiles we won't be track those files in git like we don't track the packages. As these files are big so those should be downloaded in the production environment rather than uploading them onto github.

Then django needs to understand where it will find the static files locally as well as in production environment. As we are no longer using other's CDN and downloaded the files locally.

So in settings.py we will make some changes.

Now run the command below
```
python manage.py collectstatic
```
While executing the command above the django will look at STATICFILES_DIRS mentioned in settings.py for all the static files that's gonna be needed.
Later when you need those files django you should look at STATIC_ROOT in settings. It's just copying the files from staticfiles to local cdn with some extra admin folder.

When we will be in production the local cdn is not what we will be using.

Now we can use the DJANGO template tag for the base files inside templates.

| previous
```html
<link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.css"  rel="stylesheet" />
```

!now
```
{% load static %}
<link href="{% static 'vendors/flowbite.min.css' %}"  rel="stylesheet" />
```

Now we will automate the download of static files

### Download URL to Local file helper function

Now create downloader.py to help with the downloads of static files.

### Custom Django Management Command to Pull Vendor Files

The reason we use django specific commands instead of python because it has access to the settings of django. Moreover there are some builtin commands but if we need custom commands we have to create those. To create custom commands we will create an app name commando (use any name you like)
```
python manage.py startapp commando
```

Now we will create a folder call management inside commando app and also create the init.py file
After that we will create another folder commands/__init__.py inside management folder

Now we will create a simple hello world command. For that create a hello_world.py file inside commands folder

Inside that file the class name should be Command.

Then mention the Commando app inside settings.py

Now if we run python manage.py we can see hello_world as a command. Let's test out the commnad

```
python manage.py hello_world
```

As we're now clear about how to create a custom command it's time to create the custom command to trigger our downloader.py
to download the static files and add that command to our Dockerfile as well.
We'll create that command inside vendor_pull.py
In the vendor_pull file we'll also set our destination to store the static files.

Now we have downloaded static files we need to serve those static files. We will be doing that using whitenoise function.
Once we will have certain amount user we will be use more robust service like s3 to better serve those staticfiles.

## Missing Args with Container Build in Railway

Update args in dockerfile for railway production environment

## Using WhiteNoise to Serve Django Static Files

Add whitenoise to the requirements.txt and add some middleware settings inside settings.py
In long run if we stop using whitenoise we have to remove that whitenoise middleware from settings.
Then also add the staticfiles_storage after static root inside settings.

## Configure Django to use Email with Gmail

We gonna be implementing gmail as our transactional email service. I don't recommend doing this in the long run. This is great for testing and getting everything working correctly. But in the long run you will want to have your own custom domain name not a gmail.com rather yourdomainname.com then a transactional email service.   
But for now we can use gmail in production and in our local development environment.  
Django itself SMTP authentication for emails. There are other ways to send email with django but the built in native one to django is the SMTP.

Let's start with .env file.

EMAIL_HOST -> This is gonna be the actual domain for the SMTP login.
Then put those variables in django settings and let python decouple to grab them from .env.

```
python manage.py sendtestemail --admin
```

## User Login & Registration

### Django Admin User + User Passwords

Django admin handles all the user and data inside django.  
To access admin we have to create the super user first. To make the superuse goto the root of the django project from terminal in our case it's the src folder. Now use the username and pass to create a superuser you can skip the email.

Now go the localhost:8000/admin

```
python manage.py createsuperuser
```

### Django Login View Logic

Now we gonna create a new app to handle the user related things such as changing the home page if user is authenticated, login & registration.
```
python manage.py startapp auth
```
Let's start with creating login view first inside the app. If you remember every view comes with a request and it returns back a response. 

### HTML Login Form and Template

Now we'll create the login html inside templates auth

{% csrf_token %} this is a security token so that other websites can't send automated login for users.

### Register Users the Hard Way

Let's start with register.html this time. After that we will go for creating the view for this.  
After seeing the basics how the login and registration works in django.  
We'd like to use third party to make our task easier and quicker to authenticate user using different social platforms and all.  

### Configure Django AllAuth

get the settings done for allauth then do some addition to urls.  
Then run the migrate command. Because we have added some apps to installed apps. It's always good to add the migratet command whenever we add apps to the installed apps.

### User Email Verificaiton with Django AllAuth

We can also verify users from backedn django admin for people who are in our team if needed istead of going through the email verfication.

### Better UI for Django AllAuth

We will make the AllAuth look like rest of our site by usign django AllAuth UI.  
First of all install and mention the required packages in the requirements.txt.  

Then add the apps in settings needed for allauth_ui. Next we will work on the ui by creating account directory inside templates.

Also did some changes in our base directory by adding a message.html for showing messages.

Next added that messages in account's base, template's base and home.

### Navbar Links for Auth

Head on to the nav folder inside templates and change accordingly in the navbar.html

Inside the urls.py if we give names to each url paterns then we can use that name instead of using the path in href. It' call ReverseMatch

Next to find the names for allauth links simply `http://127.0.0.1:8000/accounts/type some zibribsh here` it will show all the links name.

### Login with Google via Django AllAuth

At first add the app to apps list in settings.

Follow this link listed all providers choose whichever you need. [here](https://docs.allauth.org/en/latest/socialaccount/providers/google.html)

This video also help [video](https://www.youtube.com/watch?v=mrD5Hb43N5o)

After that there are 2 things that we really need to do. We need to create a brand new app itself inside the provider online like we did one as well for development the other one will be for production as we will be using differnt urls for production.

## Password Protected Page with Django Sessions

Once we have users we have set permissions for different type of user to allow them to do different things.  
Let's start with a password protected page. In this case user logged in or not doesn't matter as we set some code input to view the page.  
But after giving the password while we will be seeing the password protected page if we reload the page it will ask for password again.  
So we can use session in this case to hold this for a certain period of time.

In real life i can take email and show them some content in the protected page and I can store the email in django model or store the email in mailchimp for marketing purposes.

## User-required Pages via login_required Decorator (User_only_view)

Make the view first for that page and create a template of page and add to the view to urls.py

It's not about authorization it's about authentication.

## Specific type of user (Staff User Required)

It will check if someone is a staff and can access to pages or do task that are only available for staffs.  
LOGIN_URL = settings.LOGIN_URL using this so that random user can stumbled upon the protected pages and redirect to djanfo backend login for.  
This is a settings in our main configuration settings.py that we can change if we want to.  
Then add this to view methods. The idea is so that we can modify where the login url ends up being.

## User Profile View

Based on user type you can do different things. For ex. staff user can only log into the admin site but can't do anything.  
So instead of working in the backend we will be creating frontend for different user.  
This is really setting the foundation based on their billing situation or subscription for our application.  
To get there we will create a new app called profiles.  
```
python manage.py startapp profiles
```
Let's list the app in settings.  Then we'll create a urls.py file inside the profiles app directory and copy all the contents of main urls.py of cfehome and paste in new urls.py. Next we'll modify the file as needed.  
We have urls.py inside the app which means we'll have some views here as well. So add a view in the view.py file.  
Then add the view in inner urls.py,

It's time to add the app view in main urls.py which we did for all other apps. But remember for allauth we used include() instead of using only one view in urls.py. For our profiles we will be doing the same that's why we have a urls.py inside our app itself.

We can pass username to be part of the urls which gives us a very dynamic way of manage different views themselves. We can check whether the logged in user and the user profile mentioned in the url are same or not by comparing their id.  
So we can ultimately check the user logged in has the permission to see the stuff of url that they are trying reach. If it's a superuser then the answer is most probably yes but if it is not a superuser then the decision upon us maybe we'll give them the permission to visit the page or maybe not.

## User Permissions and has_perm

We'll ber seeing user permissions in django admin first and then we will see it in our view. User permissions are like access; do they to do this thing. 
In the long run we will make customized permission based on our need for users instead of using the permissions offered by django admin backend. Because once we ended up giving too many permissions they might able to do certain things for other users as well which shouldn't be the case.

We call also print out all the available permission in django backend rather than going to actual backend by using python shell.

```
python manage.py shell

from django.contrib.auth.models import Permission
qs = Permission.objects.all()
for obj in qs:
    print(obj)
```
But we want something that we can use in our actual code for giving away these permissions.

```
from django.contrib.auth.models import Permission
qs = Permission.objects.all()
for obj in qs:
    print(obj.codename)
```

If we put the codenames like this `user.has_perm(view_user)` it won't work as well. The way these code name works has to do with the model value itself. The way we can actually find them:
```
for obj in qs:
    print(obj.content_type.app_label, obj.codename)
```

so for has_perm funtion we have to set the argument like this

    # <app_label>.view_<model_name>
    # <app_label>.add_<model_name>
    # <app_label>.change_<model_name>
    # <app_label>.delete_<model_name>

`print("user.has_perm('auth.view_user')", user.has_perm("auth.view_user"))`

All these are default permission set by django. Let's try this with visits app.
Make sure while you're passing argument keep the model name in lower case. For ex. in visits app the actual model name is lije PageVisit but we would pass it in here as pagevisit

`print("visits.has_perm('visits.view_pagevisit')", user.has_perm("visits.view_pagevisit"))`

We would like to define our own permission as well but before that we would like to see how these can be used in our actual templates itself and then adjust accordingly.

## User Permissions in Django Templates

Implementing permission in the views make a lot sense instead of using checking permission in the templates because if the user doesn't have permission to view a page then we can render a generic page that says you don't have permission to see this.

## Groups & Group-level Permissions

When it comes to permission its better to use it in groups instead of adding it in individual user.  
Go to the django admin panel and click on Groups-> Add group  
To add users to this group go to individual users and add them.  
We would like have this group according to our subscription plan. For ex. Basic Plan.  These groups are gonna be separate from the actual billing. This is just for the technical ability to separate users into different groups, so their permission are related to what their group has permissions to do versus what their billing status is. 

## Custom Permission for Django Users

Basically we can change the content based off of the content that the user is in. For checking whether the user is part of a group or not we have added some example code in the `profile_details_view` .  
Now let's make a new app called `subscriptions` for custom permissions. 
```
python manage.py startapp subscriptions
```
Then create a Subscription model inside the app. We will be creating 3 custom permission as we have 3 subscriptions plan. Now after finishing modifying the models.py include the app in our main settings then run the migrations command to implement the chages.

Now go to the django backend admin panel, notice the permissions has been added to the lists. So we can add the basic permission to our basic gorup that we created earlier. And in Pro plan we should give basic and pro both permissions. Lastly in the advance plan give all the 3 permissions.  
Now instead of checking whether user is in a group or not we can check whether the user has different subscriptions plans or not which is easier because user might be in different group but having low level subscriptions.  
So group doesn't matter that much. All that's matter is whether they have the permissions or not.

## Groups and Permissions within Subscriptions

Now we want to correlate our subscription model with the groups model as well as the permissions model.  
First the add groups in our subscriptions model then register the model inside Subscriptions `admin.py`.

In Django, admin.site.register(Subscription) is used to register a model (in this case, Subscription) with the Django administration site.
When you register a model with the admin site, Django provides a basic CRUD (Create, Read, Update, Delete) interface for managing instances of that model in the admin dashboard.

Now run the migrations command and go the django admin. Holla, we can see a Subscription panel in backedn now.  
Let's go to the subscriptions and make a object. Name the object Starter and add some groups. For ex. basic groups.  
The idea here is not to manage the groups, the idea is to connect the subscriptions whatever it ends up being with the group themselves. But the other part of this is make sure of that the same subscription has specific permissions that are in the selected groups.  
For simplicity It's just to make a track of what are the users paying for. If we accedientally remove permissions from the plans we will be lost what permissions were there but if we go to the subscriptions we can see the plan name.

Now that we finished updating the Subscriptions `model.py` -> The idea here is what we will still need to advance is we need to turn every subscription we might have ensure the group has the permissions that are on there.

What we did in subsription is actually metadata. These are not handling the permissions or the group themselves but rather making sure all of these are getting tracked in some form. Let's see how we can then bring them together to make sure that the groups matched in any given subsriptions, matches the same permissions also in that subscription.

## Syncing Subscription Groups and Permissions

So let's say we remove a permission from basic plan accidentaly but the subscription plan of basic users using that permission. So we are now implementing a way so that the permission automatically assigned after getting deleted if it's been using for any given user group that are currently any subscription plan.  
So the way we gonna heal it by a custom management command. So copy the management folder from commando app and bring it into subscriptions.  
Now let's remove all the pycache folder inside the management folder.

Then modify the sync_subs.py after done with this we can modify subscriptions from backend and just run the command to sync everything without manually giving the permissions to user. 
```
python manage.py sync_subs
```

Each User can have only one user. Now we'll have to find a way to move users based off of their subscriptions.

## Sync User Subscription with the User Group (part 1)

Now we're gonna correlate user with our subscriptions. As we soon as we include a user into subscription plan from Subscription right away they should be automatically included in that user group which we can varify by going to the user from backend and checking the groups that they are in.

we used the post_save. Post_save signal gonna grab the groups for user_subscription whenever the user_subscription changes.  
Sometimes we would like to give a specific permission regardless of their group permission.

## Sync User Subscription with the User Group (part 2)

For this we will use a allow_custom_groups variable. If it's true than we will allow to have some extra permissions outside of their group permissions.

For example I can set a custom group for trial plan but the user will not be under in any subscription plan. But if they buy any subscription that will be added automatically. Even if they change subscription group other permissions and groups will change based off of the subscritions but the trial will remain there.

## Getting startted with Stripe

The idea here is some sort of reoccuring transactions and actual financial transactions that will change their user group and their permissions inside django.

To create a customer we gonna use a module under helpers called billing so that whenever we need switch platform we can easily do that by making changes to only one file.  
Now it's time to grab the actual api keys from Stripe dashboard. Once we have the keys we'll put those in our .env file. Now while we are in Debug mode we have to make sure our Stripr seceret key starts with "sk_test" otherwise it will cost us :(  
So we'll set a condition in billing.py to check whether we're in debug mode and the secrect is has sk_test in front of it or not.  

The next thing is we want integrate the customer, we want to billed out the customer.  

## Django Customer Model

Now we gonna create a place to store all our django users as stripe customers. We have to tie the stripe customer id to our actual user id in django.  
So, we gonna make a new app called customers. After that we'll create the Customers model. Then register it in admin. Next include the app in settings.py.

## Override Django Save to Create Stripe Customer

Now we goona create a create_customer function inside the `billing.py`.

## Django AllAuth Signal to Confirm Django Stripe Customers

Django AllAuth has some signal features that we can use to associate the user to stripe automatically when the registration process happens. AllAuth also has mechanism for changing email address.
After signup when they verify the email address then we go for the association with stripe.

Make sure you're not using stripe's interface to create new customer. As we already co-related customer's. 

## Our Django Subscription Model as a Stripe Product
Timestamp: 6:51:44

## Environment Variables in GitHub Actions

Copy paste the same thing from github just discarded some stufss related to checkout which hasn't been implemented properly yet.

## GitHub Actions Secrets for Database URL

Copy paste the same thing. After that added a test in commando app's test.py file.
then run the test.  
```
python manage.py test
```
Then go to the settings to add the databse url in secrets on github.

## Branch Neon DB for django tests

Copy paste from the actual repo and added secret keys to github from Neon DB.

## Scheduled Production Worker with Django Neon Stripe & GitHub Actions

Copy paste from actual repo then set the stripe secret key in github actions.  
Remember that the secret key is the development secret key. Make sure to change it with the production one once in production stage.

## A better Landing Page with Flowbite

We'll be using flowbite blocks to improve the ui. We'll be creating a new app only for the landing page. The app will be called landing.
```
python manage.py startapp landing
```
Then create the view add the url to main cfehome urls.py. Create the template landing/main.html.  
After that hero.html. We'll go to Flowbite Blocks website to choose that.

We don't necessarily need to list this app inside the installed apps as this app doesn't contain any model.

## Using the SaaS Theme to Fix Missing Tailwind Classes

Downloaded the saas-theme css and upload it to my primary google drive and save the link in vendor pull.py. It fixes some styling issues with tailwind.  

## Dashboard View

For dashboard we're gonna create new app again, called dashboard.

## Decouple Dashboard View

Now we gonna cut specific portions and make separate html files for them.  
First will create a base.html specifically for dashboard.

## Changing database from NEONDB postgres to railway postgres


## Notifications App

As we want to use django channels and websocket so we have to switched to WSGI to ASGI. WSGI is synchronous, handling one request at a time, and blocking execution until processing is complete. ASGI is asynchronous, handling multiple requests concurrently without blocking other requests.  
Currently we're using the built django WSGI server but that doesn't support websocket.  
Installed `channels` and add it to settings app list. Then add `channel_layers` at the end of the settings file. It will control the channels and groups.   
Then install channels-redis. Then add ASGI application in settings. Next go to the `asgi.py` and make necessary changes.  
Create file `consumers.py` inside notification app. Write the code here which is pasted from django documentation.  
Then create `routing.py` and write the code. It's time to implement the websocket in `asgi.py`.  
If want to send notification to particular user then check [14.00 timestamp](https://youtu.be/DqCqFRYO4W8?t=792)  

Then create the javascript for notifications. Create in template base folder so that it can be accessible for other apps.

As soon as the user makes a socket connection to the server a new channel will be created for that particular user and he will be alotted a unique id for that and the channel will be added to the group for which the user is requesting for.  
We have to pass room name in js so let's add that in views of main app.   
Now code `models.py` of notifications app.

After that implement celery by installing the package and write the settings for celery inside settings.py and then create celery.py in parent folder then create task.py inside the sub app where you will implement celery. Then add views and mention the url to urls.py.

Next we gonna add `CELERY_RESULT_BACKEND` this to settings which will inform us whether the task has been executed or still processing. Next install another package call `django-celery-results`. Add it to the installed apps.

To run celerey:
```
celery -A cfehome worker --pool=solo -l info
```

So if we run the django server and celery server and go to the view where we implemneted the celery we will see that the page will be immediately visible the celery is doing its task in another server in the background. Basically we use celery to distribute heavy task from our main server to perform in another server and get the result back to use once the task is done. In this way, the main server won't be effected and also the celery sever done tasks in queue and it's asynchronous.  

Install `django-celery-beat` and go to the settings to add things related to this. Then implement the variable `app.conf.beat_schedule` to define the time to execute the tasks automatically. Then apply the migrations. To start the celery beat
```
celery -A cfehome beat -l info
```

## Table drop

If want to drop any table use the custom command below
```
# Drop table without CASCADE
python manage.py drop_table finance_finance

# Drop table with CASCADE
python manage.py drop_table finance_finance --cascade
```

## Tailwind and Flowbite
To update the css according to our use run the follwoing command
```
npm run dev
```


## Check Database files

Check your database file - If you're using SQLite, you can verify the tables exist by opening the database file with a SQLite browser or running:

bashpython manage.py dbshell
.tables

Reset and recreate (only if you're in development and can lose data):

## Drop Database
bash# Delete the database file (if using SQLite)
rm db.sqlite3

Run migrations again
python manage.py migrate