{% extends 'finance/base_finance.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block finance_content %}
<div class="finance-form-container">
    <h2 class="text-xl font-semibold mb-4">Employer Benefits</h2>

    <!-- Summary Card -->
    <div class="bg-gray-800 p-4 rounded-lg mb-6">
        <h3 class="text-lg font-medium mb-2">Benefits Overview</h3>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <p>Total Monthly Contributions: $<span id="total-contributions">0.00</span></p>
                <p>Employer Match: $<span id="employer-match">0.00</span></p>
            </div>
            <div>
                <p>Active Benefits: <span id="active-benefits-count">0</span></p>
            </div>
        </div>
    </div>

    <!-- Add New Benefit -->
    <div class="bg-gray-800 p-4 rounded-lg mb-6">
        <h3 class="text-lg font-medium mb-4">Add New Benefit</h3>
        <form id="benefit-form" method="POST" class="space-y-4">
            {% csrf_token %}
            <input type="hidden" name="create_benefit" value="true">
            {{ benefit_form|crispy }}
            <button type="submit" class="btn btn-primary">Add Benefit</button>
        </form>
    </div>

    <!-- Active Benefits -->
    <div class="bg-gray-800 p-4 rounded-lg mb-6">
        <h3 class="text-lg font-medium mb-4">Current Benefits</h3>
        <div id="benefits-container">
            {% for benefit in employer_benefits %}
            <div class="benefit-item mb-4 p-4 border border-gray-700 rounded">
                <div class="flex justify-between items-center">
                    <h4>{{ benefit.name }}</h4>
                    <div class="space-x-2">
                        <button class="edit-benefit btn btn-secondary" data-id="{{ benefit.id }}">Edit</button>
                        <button class="toggle-benefit btn btn-warning" data-id="{{ benefit.id }}">
                            {% if benefit.is_active %}Deactivate{% else %}Activate{% endif %}
                        </button>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4 mt-2">
                    <p>Type: {{ benefit.get_benefit_type_display }}</p>
                    <p>Contribution: {{ benefit.contribution_percentage }}%</p>
                    <p>Employer Match: {{ benefit.employer_match_percentage }}%</p>
                    <p>Status: {% if benefit.is_active %}Active{% else %}Inactive{% endif %}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Monthly Payments -->
    <div class="bg-gray-800 p-4 rounded-lg">
        <h3 class="text-lg font-medium mb-4">Monthly Contributions</h3>
        
        <!-- Month/Year Selector -->
        <div class="flex space-x-4 mb-4">
            <input type="month" id="payment-month-filter" class="form-input">
            <button id="search-payments" class="btn btn-secondary">Search</button>
        </div>

        <!-- Payments Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr>
                        <th>Benefit</th>
                        <th>Date</th>
                        <th>Employee Contribution</th>
                        <th>Employer Match</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="payments-table">
                    {% for payment in benefit_payments %}
                    <tr>
                        <td>{{ payment.employer_benefits.name }}</td>
                        <td>{{ payment.payment_date }}</td>
                        <td>${{ payment.employee_contribution }}</td>
                        <td>${{ payment.employer_match }}</td>
                        <td>
                            <button class="edit-payment" data-id="{{ payment.id }}">Edit</button>
                            <button class="delete-payment" data-id="{{ payment.id }}">Delete</button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block finance_js %}
<script src="{% static 'finance/employer_benefits.js' %}"></script>
{% endblock %}


