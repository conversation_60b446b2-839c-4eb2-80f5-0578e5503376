document.addEventListener('DOMContentLoaded', function() {
    // Initialize elements
    const addGoalBtn = document.getElementById('add-goal-btn');
    const goalForm = document.getElementById('goal-form');
    const prevButton = document.getElementById('prev-step');
    const completePlanButton = document.getElementById('complete-plan');
    const contributionMonthFilter = document.getElementById('contribution-month-filter');
    
    // Get modal elements
    const goalModal = document.getElementById('goal-modal');
    const modalOptions = {
        backdrop: 'dynamic',
        backdropClasses: 'bg-gray-900 bg-opacity-50 dark:bg-opacity-80 fixed inset-0 z-40',
        closable: true
    };
    
    const goalModalInstance = new Modal(goalModal, modalOptions);
    
    let currentGoalId = null;

    // Initialize the month filter with current month
    const today = new Date();
    contributionMonthFilter.value = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;

    // Update summary statistics
    function updateSummaryStats() {
        const goals = document.querySelectorAll('tbody tr:not(.empty-row)');
        const completedGoals = Array.from(goals).filter(row => {
            const progressText = row.querySelector('td:nth-child(5) span').textContent;
            return parseInt(progressText) === 100;
        });
        
        let totalSaved = 0;
        goals.forEach(row => {
            const currentAmount = parseFloat(row.querySelector('td:nth-child(4)').textContent.replace('$', '').replace(',', ''));
            totalSaved += currentAmount;
        });

        document.getElementById('total-goals').textContent = goals.length;
        document.getElementById('completed-goals').textContent = completedGoals.length;
        document.getElementById('total-saved').textContent = totalSaved.toFixed(2);
    }

    // Show modal for adding new goal
    addGoalBtn.addEventListener('click', () => {
        currentGoalId = null;
        goalForm.reset();
        goalModalInstance.show();
    });

    // Handle edit goal button clicks
    document.querySelectorAll('.edit-goal').forEach(button => {
        button.addEventListener('click', (e) => {
            const goalId = e.target.dataset.id;
            currentGoalId = goalId;
            
            // Fetch goal data and populate form
            fetch(`/finance/get_goal/${goalId}/`)
                .then(response => response.json())
                .then(data => {
                    Object.keys(data).forEach(key => {
                        const input = goalForm.querySelector(`[name="${key}"]`);
                        if (input) {
                            if (input.type === 'date') {
                                input.value = data[key].split('T')[0];
                            } else {
                                input.value = data[key];
                            }
                        }
                    });
                    goalModalInstance.show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error fetching goal data');
                });
        });
    });

    // Handle delete goal button clicks
    document.querySelectorAll('.delete-goal').forEach(button => {
        button.addEventListener('click', (e) => {
            if (confirm('Are you sure you want to delete this goal?')) {
                const goalId = e.target.dataset.id;
                fetch(`/finance/delete_goal/${goalId}/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        window.location.reload();
                    } else {
                        alert('Error deleting goal');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting goal');
                });
            }
        });
    });

    // Handle form submission
    goalForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const formData = new FormData(goalForm);
        
        if (currentGoalId) {
            formData.append('update_goal', '1');
            formData.append('goal_id', currentGoalId);
        } else {
            formData.append('create_goal', '1');
        }

        fetch('/finance/financial_goals/', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                window.location.reload();
            } else {
                Object.keys(data.errors).forEach(key => {
                    const input = goalForm.querySelector(`[name="${key}"]`);
                    if (input) {
                        input.classList.add('error');
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'error-message text-red-600 text-sm mt-1';
                        errorDiv.textContent = data.errors[key];
                        input.parentNode.appendChild(errorDiv);
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while saving the goal.');
        });
    });

    // Handle contribution search
    document.getElementById('search-contributions').addEventListener('click', () => {
        const monthYear = contributionMonthFilter.value;
        if (!monthYear) return;

        const [year, month] = monthYear.split('-');
        fetch(`/finance/get_goal_payments/${year}/${month}/`)
            .then(response => response.json())
            .then(data => {
                const tbody = document.getElementById('contributions-table');
                tbody.innerHTML = '';

                if (data.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="4" class="px-4 py-4 text-center text-gray-500">
                                No contributions found for the selected period.
                            </td>
                        </tr>
                    `;
                    return;
                }

                data.forEach(payment => {
                    tbody.innerHTML += `
                        <tr class="border-t">
                            <td class="px-4 py-2">${payment.goal_name}</td>
                            <td class="px-4 py-2">${payment.payment_date}</td>
                            <td class="px-4 py-2 text-right">$${payment.amount.toFixed(2)}</td>
                            <td class="px-4 py-2 text-center">
                                <button class="edit-payment btn-secondary" data-id="${payment.id}">Edit</button>
                                <button class="delete-payment btn-danger" data-id="${payment.id}">Delete</button>
                            </td>
                        </tr>
                    `;
                });
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error fetching contribution data');
            });
    });

    // Handle navigation buttons
    prevButton.addEventListener('click', () => {
        window.location.href = '/finance/investment_accounts/';
    });

    completePlanButton.addEventListener('click', () => {
        window.location.href = '/finance/complete-plan/';
    });

    // Clear validation errors when input changes
    goalForm.querySelectorAll('input, select').forEach(input => {
        input.addEventListener('input', () => {
            input.classList.remove('error');
            const errorMessage = input.parentNode.querySelector('.error-message');
            if (errorMessage) {
                errorMessage.remove();
            }
        });
    });

    // Initialize summary stats on page load
    updateSummaryStats();
});

