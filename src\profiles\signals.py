from django.conf import settings
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Profile

# Make sure to create the Profile when a User is created
@receiver(post_save, sender=settings.AUTH_USER_MODEL)
def create_profile(sender, instance, created, **kwargs):
    if created:
        Profile.objects.create(user=instance)

# # any changes to the user instance are also reflected in the associated profile instance
# @receiver(post_save, sender=settings.AUTH_USER_MODEL)
# def save_profile(sender, instance, **kwargs):
#     instance.profile.save()