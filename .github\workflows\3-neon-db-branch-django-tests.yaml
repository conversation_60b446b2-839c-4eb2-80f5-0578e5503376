name: 3-<PERSON> Neon DB for Django Tests

on:
  workflow_dispatch:
  # push:
  #   branches:
  #     - main

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      DJANGO_DEBUG: 0
      # DATABASE_URL: ${{ secrets.NEON_DATABASE_URL }}
      NEON_API_KEY: ${{ secrets.NEON_API_KEY }}
      NEON_PROJECT_ID: ${{ secrets.NEON_PROJECT_ID }}
      NEON_PROD_BRANCH: "main"
      NEON_GITHUB_BRANCH: "gh_delete"
      # DJANGO_SECRET_KEY: not-good
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: "20.11"

      - name: Install Neon CLI
        run: |
          npm install -g neonctl

      - name: Delete previous branch
        continue-on-error: true
        run: |
          neonctl branches delete "${{ env.NEON_GITHUB_BRANCH }}" --project-id "${{ env.NEON_PROJECT_ID }}"

      - name: Create new Github Actions branch
        run: |
          neonctl branches create --name "${{ env.NEON_GITHUB_BRANCH }}" --parent "${{ env.NEON_PROD_BRANCH }}" --project-id "${{ env.NEON_PROJECT_ID }}"
      
      - name: Database URL Env Val for new Github Actions branch
        run: |
          MY_NEON_CONN_STRING=$(neonctl connection-string --branch "${{ env.NEON_GITHUB_BRANCH }}" --project-id "${{ env.NEON_PROJECT_ID }}")
          echo "DATABASE_URL=$MY_NEON_CONN_STRING" >> $GITHUB_ENV
      
      - name: Setup Django Secret Key
        run: |
          MY_GEN_KEY=$(openssl rand -base64 32)
          echo "DJANGO_SECRET_KEY=$MY_GEN_KEY" >> $GITHUB_ENV
      
      - name: Install Requirements
        run: |
          python -m pip install pip --upgrade
          python -m pip install -r requirements.txt
      
      - name: Django Migrate Database
        working-directory: ./src
        run: |
          python manage.py migrate
      
      - name: Django Tests
        working-directory: ./src
        run: |
          python manage.py test
      
      - name: Clean Up Step
        continue-on-error: true
        run: |
          neonctl branches delete "${{ env.NEON_GITHUB_BRANCH }}" --project-id "${{ env.NEON_PROJECT_ID }}"