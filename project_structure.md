.github/
    └── workflows/
        ├── 1-test-django-env-vars.yaml
        ├── 2-test-django-database-url.yaml
        ├── 3-neon-db-branch-django-tests.yaml
        ├── 4-scheduled-production-worker.yaml
        └── railway-production-worker.yaml
assets/
    ├── flowchart.dot
    └── graphviz.svg
src/
    ├── activity_log/
        ├── migrations/
            ├── __init__.py
            └── 0001_initial.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── middleware.py
        ├── models.py
        ├── tests.py
        └── views.py
    ├── auth/
        ├── migrations/
            └── __init__.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── models.py
        ├── tests.py
        └── views.py
    ├── cfehome/
        ├── __init__.py
        ├── asgi.py
        ├── celery.py
        ├── forms.py
        ├── settings.py
        ├── urls.py
        ├── views.py
        └── wsgi.py
    ├── commando/
        ├── management/
            ├── commands/
                ├── __init__.py
                ├── drop_table.py
                ├── hello_world.py
                └── vendor_pull.py
            └── __init__.py
        ├── migrations/
            └── __init__.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── models.py
        ├── tests.py
        └── views.py
    ├── contact/
        ├── migrations/
            ├── __init__.py
            └── 0001_initial.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── models.py
        ├── tests.py
        └── views.py
    ├── customers/
        ├── migrations/
            ├── __init__.py
            └── 0001_initial.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── models.py
        ├── tests.py
        └── views.py
    ├── dashboard/
        ├── migrations/
            ├── __init__.py
            └── 0001_initial.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── custom_context_processors.py
        ├── models.py
        ├── tests.py
        └── views.py
    ├── finance/
        ├── migrations/
            ├── __init__.py
            └── 0001_initial.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── forms.py
        ├── models.py
        ├── tests.py
        ├── urls.py
        └── views.py
    ├── helpers/
        ├── __init__.py
        ├── billing.py
        ├── downloader.py
        └── validators.py
    ├── landing/
        ├── migrations/
            └── __init__.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── models.py
        ├── tests.py
        └── views.py
    ├── notifications/
        ├── migrations/
            ├── __init__.py
            └── 0001_initial.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── consumers.py
        ├── models.py
        ├── routing.py
        ├── tasks.py
        ├── tests.py
        └── views.py
    ├── profiles/
        ├── migrations/
            ├── __init__.py
            └── 0001_initial.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── models.py
        ├── signals.py
        ├── tests.py
        ├── urls.py
        └── views.py
    ├── send_email/
        ├── migrations/
            └── __init__.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── models.py
        ├── signals.py
        ├── tasks.py
        ├── tests.py
        └── views.py
    ├── staticfiles/
        ├── activity_log/
            └── session_expiry.js
        ├── css/
            └── ui.css
        ├── finance/
            ├── debt_management.js
            ├── emergency_fund.js
            ├── employer_benefits.js
            ├── finance.js
            ├── financial_goals.js
            ├── investment_accounts.js
            └── monthly_overview.js
        ├── images/
            ├── hero_1.png
            └── hero_2.jpg
        ├── js/
            └── alerts.js
        ├── src/
            └── tailwind-input.css
        └── vendors/
            ├── flowbite.min.css
            ├── flowbite.min.js
            ├── flowbite.min.js.map
            └── saas-theme.min.css
    ├── subscriptions/
        ├── management/
            ├── commands/
                ├── __init__.py
                └── sync_subs.py
            └── __init__.py
        ├── migrations/
            ├── __init__.py
            └── 0001_initial.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── models.py
        ├── tests.py
        └── views.py
    ├── templates/
        ├── account/
            └── base.html
        ├── auth/
            ├── login.html
            └── register.html
        ├── base/
            ├── activity_log_js.html
            ├── css.html
            ├── finance_css.html
            ├── finance_js.html
            ├── google_tag_js.html
            ├── js.html
            ├── loading_spinner.html
            ├── messages.html
            ├── notifications_js.html
            ├── popup_modal.html
            └── theme_switcher.html
        ├── contact/
            ├── form.html
            └── main.html
        ├── dashboard/
            ├── application_shell.html
            ├── base.html
            ├── main.html
            ├── nav_full.html
            ├── nav.html
            ├── notifications.html
            ├── sidebar_full.html
            └── sidebar.html
        ├── finance/
            ├── base_finance.html
            ├── debt_management.html
            ├── emergency_fund.html
            ├── employer_benefits.html
            ├── financial_goals.html
            ├── investment_accounts.html
            └── monthly_overview.html
        ├── footer/
            └── footer.html
        ├── landing/
            ├── features.html
            ├── hero.html
            ├── main.html
            └── proof.html
        ├── nav/
            └── navbar.html
        ├── newsletter/
            └── newsletter.html
        ├── profiles/
            ├── details.html
            └── user_list.html
        ├── protected/
            ├── entry.html
            ├── staff-only.html
            ├── user-only.html
            └── view.html
        ├── snippets/
            └── welcome_user_msg.html
        ├── 404.html
        ├── about.html
        └── base.html
    ├── visits/
        ├── migrations/
            ├── __init__.py
            └── 0001_initial.py
        ├── __init__.py
        ├── admin.py
        ├── apps.py
        ├── models.py
        ├── tests.py
        └── views.py
    └── manage.py
.cursorindexingignore
.gitignore
Dockerfile
LICENSE
llm_suggetions.md
package-lock.json
package.json
PROGRESS.md
railway.toml
raw_readme.md
README.md
requirements.txt
tailwind.config.js