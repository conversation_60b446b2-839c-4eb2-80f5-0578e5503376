document.addEventListener('DOMContentLoaded', function() {
    console.log('Emergency fund JS loaded');
    const contributionForm = document.getElementById('contribution-form');
    const fundSettingsForm = document.getElementById('fund-settings-form');
    const editContributionForm = document.getElementById('edit-contribution-form');
    const editModal = document.getElementById('editContributionModal');
    const previousButton = document.getElementById('previous-button');
    const nextButton = document.getElementById('next-button');
    
    // Navigation buttons
    if (previousButton) {
        previousButton.addEventListener('click', function() {
            window.location.href = '/finance/monthly-overview/';
        });
    }
    
    if (nextButton) {
        nextButton.addEventListener('click', function() {
            window.location.href = '/finance/debt-management/';
        });
    }
    
    // Function to show loading indicator
    function showLoadingIndicator() {
        document.querySelector('.loading-indicator').style.display = 'flex';
    }
    
    // Function to hide loading indicator
    function hideLoadingIndicator() {
        document.querySelector('.loading-indicator').style.display = 'none';
    }
    
    // Function to get CSRF token
    function getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value;
    }
    
    // Function to show the edit modal
    function showEditModal() {
        editModal.classList.remove('hidden');
        editModal.classList.add('flex');
    }
    
    // Function to hide the edit modal
    function hideEditModal() {
        editModal.classList.add('hidden');
        editModal.classList.remove('flex');
    }
    
    // Add event listeners for modal close buttons
    document.getElementById('close-edit-modal').addEventListener('click', hideEditModal);
    document.getElementById('cancel-edit-btn').addEventListener('click', hideEditModal);
    
    // Debug check for AlertSystem
    console.log('AlertSystem available:', typeof AlertSystem !== 'undefined');
    
    // Handle contribution form submission
    if (contributionForm) {
        contributionForm.addEventListener('submit', function(e) {
            console.log('Contribution form submitted');
            // Always prevent default for AJAX handling
            e.preventDefault();
            showLoadingIndicator();
            
            // Get form data
            const formData = new FormData(contributionForm);
            formData.append('add_contribution', '1');
            
            // Send AJAX request
            fetch(window.location.pathname, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingIndicator();
                console.log('Response received:', data);
                
                if (data.status === 'success') {
                    // Check if AlertSystem is available
                    if (typeof AlertSystem !== 'undefined') {
                        console.log('Using AlertSystem for success');
                        AlertSystem.showSuccess('Contribution added successfully.', () => {
                            contributionForm.reset();
                            // Set today's date as default
                            const today = new Date().toISOString().split('T')[0];
                            document.querySelector('input[name="contribution_date"]').value = today;
                            
                            // Reload the page to show updated values
                            window.location.reload();
                        });
                    } else {
                        console.log('Using standard alert for success');
                        alert('Contribution added successfully.');
                        contributionForm.reset();
                        window.location.reload();
                    }
                } else {
                    let errorMessage = 'Please correct the following errors:<br>';
                    Object.entries(data.errors).forEach(([field, errors]) => {
                        errorMessage += `- ${field}: ${errors.join(', ')}<br>`;
                    });
                    
                    if (typeof AlertSystem !== 'undefined') {
                        console.log('Using AlertSystem for error');
                        AlertSystem.showError(errorMessage);
                    } else {
                        console.log('Using standard alert for error');
                        alert(errorMessage.replace(/<br>/g, '\n'));
                    }
                }
            })
            .catch(error => {
                hideLoadingIndicator();
                console.error('Error:', error);
                
                if (typeof AlertSystem !== 'undefined') {
                    AlertSystem.showError('An error occurred while processing your request.');
                } else {
                    alert('An error occurred while processing your request.');
                }
            });
        });
    }
    
    // Handle fund settings form submission
    if (fundSettingsForm) {
        fundSettingsForm.addEventListener('submit', function(e) {
            console.log('Fund settings form submitted');
            // Always prevent default for AJAX handling
            e.preventDefault();
            showLoadingIndicator();
            
            // Get form data
            const formData = new FormData(fundSettingsForm);
            formData.append('update_fund', '1');
            
            // Send AJAX request
            fetch(window.location.pathname, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingIndicator();
                console.log('Response received:', data);
                
                if (data.status === 'success') {
                    if (typeof AlertSystem !== 'undefined') {
                        console.log('Using AlertSystem for success');
                        AlertSystem.showSuccess('Emergency fund settings updated successfully.', () => {
                            // Reload the page to show updated values
                            window.location.reload();
                        });
                    } else {
                        console.log('Using standard alert for success');
                        alert('Emergency fund settings updated successfully.');
                        window.location.reload();
                    }
                } else {
                    let errorMessage = 'Please correct the following errors:<br>';
                    Object.entries(data.errors).forEach(([field, errors]) => {
                        errorMessage += `- ${field}: ${errors.join(', ')}<br>`;
                    });
                    
                    if (typeof AlertSystem !== 'undefined') {
                        console.log('Using AlertSystem for error');
                        AlertSystem.showError(errorMessage);
                    } else {
                        console.log('Using standard alert for error');
                        alert(errorMessage.replace(/<br>/g, '\n'));
                    }
                }
            })
            .catch(error => {
                hideLoadingIndicator();
                console.error('Error:', error);
                
                if (typeof AlertSystem !== 'undefined') {
                    AlertSystem.showError('An error occurred while processing your request.');
                } else {
                    alert('An error occurred while processing your request.');
                }
            });
        });
    }
    
    // Handle edit contribution form submission
    if (editContributionForm) {
        editContributionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            showLoadingIndicator();
            
            const contributionId = document.getElementById('edit-contribution-id').value;
            const formData = new FormData(editContributionForm);
            
            fetch(`/finance/emergency-fund/contribution/${contributionId}/edit/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingIndicator();
                console.log('Edit response:', data); // Add logging to see the response
                
                if (data.status === 'success') {
                    if (typeof AlertSystem !== 'undefined') {
                        AlertSystem.showSuccess('Contribution updated successfully.', () => {
                            // Hide the modal
                            hideEditModal();
                            
                            // Reload the page to show updated values
                            window.location.reload();
                        });
                    } else {
                        alert('Contribution updated successfully.');
                        hideEditModal();
                        window.location.reload();
                    }
                } else {
                    let errorMessage = 'Please correct the following errors:';
                    
                    // Check if there are errors in the response
                    if (data.errors && Object.keys(data.errors).length > 0) {
                        errorMessage += '<br>';
                        Object.entries(data.errors).forEach(([field, errors]) => {
                            if (Array.isArray(errors)) {
                                errorMessage += `- ${field}: ${errors.join(', ')}<br>`;
                            } else if (typeof errors === 'string') {
                                errorMessage += `- ${field}: ${errors}<br>`;
                            }
                        });
                    } else if (data.message) {
                        // If there's a general message error
                        errorMessage += '<br>- ' + data.message;
                    } else {
                        // If no specific errors are provided
                        errorMessage += '<br>- An unknown error occurred. Please try again.';
                    }
                    
                    if (typeof AlertSystem !== 'undefined') {
                        AlertSystem.showError(errorMessage);
                    } else {
                        alert(errorMessage.replace(/<br>/g, '\n'));
                    }
                }
            })
            .catch(error => {
                hideLoadingIndicator();
                console.error('Error:', error);
                
                if (typeof AlertSystem !== 'undefined') {
                    AlertSystem.showError('An error occurred while processing your request.');
                } else {
                    alert('An error occurred while processing your request.');
                }
            });
        });
    }
    
    // Handle edit and delete buttons for contributions
    document.addEventListener('click', function(e) {
        // Edit contribution button
        if (e.target.closest('.edit-contribution')) {
            const button = e.target.closest('.edit-contribution');
            const contributionId = button.dataset.id;
            
            // Fetch contribution details
            fetch(`/finance/emergency-fund/contribution/${contributionId}/`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Populate the edit form
                    document.getElementById('edit-contribution-id').value = data.contribution.id;
                    document.getElementById('edit-contribution-amount').value = data.contribution.contribution_amount;
                    document.getElementById('edit-contribution-date').value = data.contribution.contribution_date;
                    document.getElementById('edit-contribution-notes').value = data.contribution.notes || '';
                    
                    // Show the modal
                    showEditModal();
                } else {
                    if (typeof AlertSystem !== 'undefined') {
                        AlertSystem.showError('Failed to load contribution details.');
                    } else {
                        alert('Failed to load contribution details.');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof AlertSystem !== 'undefined') {
                    AlertSystem.showError('An error occurred while loading contribution details.');
                } else {
                    alert('An error occurred while loading contribution details.');
                }
            });
        }
        
        // Delete contribution button
        if (e.target.closest('.delete-contribution')) {
            const button = e.target.closest('.delete-contribution');
            const contributionId = button.dataset.id;
            
            if (confirm('Are you sure you want to delete this contribution? This will reduce your emergency fund balance.')) {
                showLoadingIndicator();
                
                fetch(`/finance/emergency-fund/contribution/${contributionId}/delete/`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': getCsrfToken()
                    }
                })
                .then(response => response.json())
                .then(data => {
                    hideLoadingIndicator();
                    
                    if (data.status === 'success') {
                        if (typeof AlertSystem !== 'undefined') {
                            AlertSystem.showSuccess('Contribution deleted successfully.', () => {
                                window.location.reload();
                            });
                        } else {
                            alert('Contribution deleted successfully.');
                            window.location.reload();
                        }
                    } else {
                        if (typeof AlertSystem !== 'undefined') {
                            AlertSystem.showError(data.message || 'Failed to delete contribution.');
                        } else {
                            alert(data.message || 'Failed to delete contribution.');
                        }
                    }
                })
                .catch(error => {
                    hideLoadingIndicator();
                    console.error('Error:', error);
                    
                    if (typeof AlertSystem !== 'undefined') {
                        AlertSystem.showError('An error occurred while processing your request.');
                    } else {
                        alert('An error occurred while processing your request.');
                    }
                });
            }
        }
    });
    
    // Set today's date as default for new contributions
    const dateInput = document.querySelector('input[name="contribution_date"]');
    if (dateInput && !dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
});
