from __future__ import absolute_import, unicode_literals
import os

from celery import Celery
from celery.schedules import crontab
from django.conf import settings

# set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cfehome.settings')

app = Celery('cfehome')
app.conf.enable_utc = False

app.conf.update(timezone='US/Pacific')

app.config_from_object(settings, namespace='CELERY')

# CELERY BEAT SETTINGS
app.conf.beat_schedule = {
    'schedule-feedback-emails': {
        'task': 'send_email.tasks.schedule_feedback_emails',
        'schedule': crontab(hour=18, minute=41),  # Runs daily at midnight
    },
}

app.autodiscover_tasks()

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')