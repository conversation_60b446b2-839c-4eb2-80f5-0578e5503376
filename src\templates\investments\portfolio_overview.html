{% extends 'investments/base_investments.html' %}
{% load static %}

{% block investments_content %}
<div class="finance-form-container">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-2xl font-bold mb-6 text-primary">Portfolio Overview</h1>
        
        <!-- Portfolio Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div class="summary-card income-card">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">Total Value</h3>
                    <p class="card-value">${{ total_portfolio_value|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
            
            <div class="summary-card expense-card">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 0v8m0 0h-6m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V9z" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">Cost Basis</h3>
                    <p class="card-value">${{ total_cost_basis|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
            
            <div class="summary-card {% if total_unrealized_pl >= 0 %}savings-card{% else %}expense-card{% endif %}">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">Unrealized P&L</h3>
                    <p class="card-value {% if total_unrealized_pl >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                        ${{ total_unrealized_pl|floatformat:2|default:"0.00" }}
                    </p>
                </div>
            </div>

            <div class="summary-card">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">Portfolios</h3>
                    <p class="card-value">{{ portfolios|length|default:"0" }}</p>
                </div>
            </div>
        </div>

        <div class="intro-text mb-6">
            <p class="text-sm opacity-75">
                Monitor your investment portfolio performance across all brokerages and asset classes.
            </p>
        </div>

        <!-- Portfolio List -->
        {% if portfolios %}
        <div class="form-section">
            <h2 class="section-title">Your Portfolios</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for portfolio in portfolios %}
                <div class="summary-card">
                    <div class="card-content">
                        <h3 class="card-label">{{ portfolio.name }}</h3>
                        <p class="text-sm text-gray-600 mb-2">{{ portfolio.description|default:"No description" }}</p>
                        <p class="card-value">{{ portfolio.base_currency.code }}</p>
                        <div class="mt-4 flex space-x-2">
                            <a href="{% url 'positions_list' %}?portfolio={{ portfolio.id }}" class="btn-secondary text-sm">View Positions</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
        <div class="form-section">
            <div class="text-center py-12">
                <svg class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Portfolios Found</h3>
                <p class="text-gray-600 mb-6">Start by adding your first investment transaction to create a portfolio.</p>
                <a href="{% url 'transaction_create' %}" class="btn-save">Add First Transaction</a>
            </div>
        </div>
        {% endif %}

        <!-- Quick Actions -->
        <div class="form-section">
            <h2 class="section-title">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{% url 'transaction_create' %}" class="action-card">
                    <div class="icon-container">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-medium">Add Transaction</h3>
                        <p class="text-sm text-gray-600">Record a new buy/sell transaction</p>
                    </div>
                </a>
                
                <a href="{% url 'positions_list' %}" class="action-card">
                    <div class="icon-container">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-medium">View Positions</h3>
                        <p class="text-sm text-gray-600">See all current holdings</p>
                    </div>
                </a>
                
                <a href="{% url 'performance_analytics' %}" class="action-card">
                    <div class="icon-container">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-medium">Analytics</h3>
                        <p class="text-sm text-gray-600">Performance metrics and charts</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock investments_content %}
