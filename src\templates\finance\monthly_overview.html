{% extends 'finance/base_finance.html' %}
{% load crispy_forms_tags %}
{% load static %}

{% block finance_content %}
<div class="finance-form-container">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6 text-primary">Monthly Overview</h1>
        
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="summary-card income-card">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">Total Income</h3>
                    <p id="total-income" class="card-value">$0.00</p>
                </div>
            </div>
            
            <div class="summary-card expense-card">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">Total Expenses</h3>
                    <p id="total-expenses" class="card-value">$0.00</p>
                </div>
            </div>
            
            <div class="summary-card savings-card">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">Total Savings</h3>
                    <p id="total-savings" class="card-value">$0.00</p>
                </div>
            </div>
        </div>

        <div class="intro-text mb-6">
            <p class="text-sm opacity-75">
                Track your monthly income and expenses to understand your financial health. Select a month and year to view or edit existing data, or enter new data to save.
            </p>
        </div>

        <form id="finance-form" method="post" class="finance-form">
            {% csrf_token %}

            <div class="date-selector mb-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.month|as_crispy_field }}
                    </div>
                    <div class="form-field">
                        {{ finance_form.year|as_crispy_field }}
                    </div>
                </div>
            </div>

            <div id="data-message"></div>

            <!-- Income Section -->
            <div class="form-section income-section">
                <h2 class="section-title">Income</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.income|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Your monthly income at hand</p>
                    </div>
                </div>
            </div>

            <!-- Expenses Section -->
            <div class="form-section expenses-section">
                <h2 class="section-title">Housing & Essentials</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.rent_or_mortgage|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Monthly rent (Including renters or homeowners insurance)</p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.food_grocery|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Monthly expenses on food and groceries</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.utilities|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Expenses on power, water, heat, phone/internet</p>
                    </div>
                </div>
            </div>

            <div class="form-section lifestyle-section">
                <h2 class="section-title">Transportation & Health</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.transportation|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Expenses on transportation, car fuel, public transit</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.insurance|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Insurance expenses (excluding renters/homeowners)</p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.healthcare|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Healthcare-related expenses</p>
                    </div>
                </div>
            </div>

            <div class="form-section personal-section">
                <h2 class="section-title">Personal & Discretionary</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.entertainment|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Expenses on entertainment</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.shopping|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Expenses on shopping</p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.personal_care|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Expenses on personal care and toiletries</p>
                    </div>
                </div>
            </div>

            <div class="form-section savings-section">
                <h2 class="section-title">Savings</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ finance_form.saving_tax_amount|as_crispy_field }}
                    </div>
                    <div class="field-info">
                        <p>Amount you save monthly for taxes (Better to keep it in high savings account)</p>
                    </div>
                </div>
            </div>

            <div class="form-actions mt-8">
                <button type="submit" id="submit-button" class="btn-save">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    Save Changes
                </button>
            </div>
        </form>

        <!-- Navigation Buttons -->
        <div class="navigation-buttons mt-12 mb-6">
            <button id="previous-button" class="nav-button prev-button">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Dashboard
            </button>
            <button id="next-button" class="nav-button next-button">
                Emergency Fund
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        </div>
    </div>

    {% block extra_content %}
    {% include 'base/loading_spinner.html' %}
    {% include 'base/popup_modal.html' %}
    {% endblock extra_content %}
</div>
{% endblock %}

{% block finance_js %}
<script src="{% static 'finance/finance.js' %}"></script>
<script src="{% static 'finance/monthly_overview.js' %}"></script>
{% endblock %}