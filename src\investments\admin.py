from django.contrib import admin
from .models import (
    Brokerage, Currency, CurrencyConversionRate, Asset, Owner,
    Transaction, TransactionOwnership, DividendIncome, DividendOwnership,
    TaxLot, TaxLotSale, Portfolio, Position, PerformanceSnapshot,
    Alert, CashTransaction
)

@admin.register(Brokerage)
class BrokerageAdmin(admin.ModelAdmin):
    list_display = ['name', 'account_number', 'user', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'account_number']

@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'symbol']
    search_fields = ['code', 'name']

@admin.register(CurrencyConversionRate)
class CurrencyConversionRateAdmin(admin.ModelAdmin):
    list_display = ['from_currency', 'to_currency', 'rate', 'date', 'source']
    list_filter = ['date', 'source']
    search_fields = ['from_currency__code', 'to_currency__code']

@admin.register(Asset)
class AssetAdmin(admin.ModelAdmin):
    list_display = ['ticker', 'name', 'asset_class', 'asset_type', 'sector', 'currency', 'is_active']
    list_filter = ['asset_class', 'asset_type', 'sector', 'currency', 'is_active']
    search_fields = ['ticker', 'name']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(Owner)
class OwnerAdmin(admin.ModelAdmin):
    list_display = ['name', 'user', 'email', 'default_currency', 'is_active']
    list_filter = ['is_active', 'default_currency']
    search_fields = ['name', 'email']

class TransactionOwnershipInline(admin.TabularInline):
    model = TransactionOwnership
    extra = 1

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ['asset', 'action', 'quantity', 'price', 'date', 'brokerage', 'currency']
    list_filter = ['action', 'date', 'brokerage', 'currency', 'asset__asset_class']
    search_fields = ['asset__ticker', 'asset__name', 'notes']
    readonly_fields = ['created_at', 'updated_at', 'net_amount', 'total_cost']
    inlines = [TransactionOwnershipInline]
    date_hierarchy = 'date'

class DividendOwnershipInline(admin.TabularInline):
    model = DividendOwnership
    extra = 1

@admin.register(DividendIncome)
class DividendIncomeAdmin(admin.ModelAdmin):
    list_display = ['asset', 'date', 'income_type', 'total_amount', 'currency', 'is_reinvested']
    list_filter = ['income_type', 'date', 'currency', 'is_reinvested']
    search_fields = ['asset__ticker', 'asset__name']
    inlines = [DividendOwnershipInline]
    date_hierarchy = 'date'

@admin.register(TaxLot)
class TaxLotAdmin(admin.ModelAdmin):
    list_display = ['asset', 'owner', 'purchase_date', 'remaining_quantity', 'current_cost_basis_per_share', 'is_closed']
    list_filter = ['is_closed', 'is_short', 'purchase_date', 'asset__asset_class']
    search_fields = ['asset__ticker', 'owner__name']
    readonly_fields = ['current_cost_basis_per_share', 'current_total_cost_basis', 'created_at', 'updated_at']

@admin.register(TaxLotSale)
class TaxLotSaleAdmin(admin.ModelAdmin):
    list_display = ['tax_lot', 'quantity_sold', 'sale_price', 'realized_gain_loss', 'is_long_term']
    list_filter = ['is_long_term', 'created_at']
    search_fields = ['tax_lot__asset__ticker']
    readonly_fields = ['proceeds', 'realized_gain_loss', 'is_long_term', 'created_at']

@admin.register(Portfolio)
class PortfolioAdmin(admin.ModelAdmin):
    list_display = ['name', 'owner', 'base_currency', 'default_tax_lot_method', 'is_active']
    list_filter = ['base_currency', 'default_tax_lot_method', 'is_active']
    search_fields = ['name', 'owner__name']

@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    list_display = ['asset', 'portfolio', 'quantity', 'average_cost', 'market_value', 'unrealized_gain_loss']
    list_filter = ['is_short', 'is_active', 'asset__asset_class']
    search_fields = ['asset__ticker', 'portfolio__name']
    readonly_fields = ['unrealized_gain_loss_percent', 'total_return', 'total_return_percent']

@admin.register(PerformanceSnapshot)
class PerformanceSnapshotAdmin(admin.ModelAdmin):
    list_display = ['portfolio', 'date', 'total_value', 'total_return_percent', 'sharpe_ratio']
    list_filter = ['date', 'portfolio']
    search_fields = ['portfolio__name']
    date_hierarchy = 'date'

@admin.register(Alert)
class AlertAdmin(admin.ModelAdmin):
    list_display = ['title', 'owner', 'alert_type', 'is_active', 'is_triggered', 'created_at']
    list_filter = ['alert_type', 'is_active', 'is_triggered', 'created_at']
    search_fields = ['title', 'owner__name', 'asset__ticker']

@admin.register(CashTransaction)
class CashTransactionAdmin(admin.ModelAdmin):
    list_display = ['portfolio', 'transaction_type', 'amount', 'currency', 'date']
    list_filter = ['transaction_type', 'currency', 'date']
    search_fields = ['portfolio__name', 'description']
    date_hierarchy = 'date'
