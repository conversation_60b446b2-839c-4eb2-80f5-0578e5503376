{% extends 'investments/base_investments.html' %}
{% load static %}

{% block investments_content %}
<div class="finance-form-container">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-2xl font-bold mb-6 text-primary">Investment Transactions</h1>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div class="summary-card income-card">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">Total Transactions</h3>
                    <p class="card-value">{{ transactions|length }}</p>
                </div>
            </div>

            <div class="summary-card expense-card">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">Total Value</h3>
                    <p class="card-value">$0.00</p>
                </div>
            </div>

            <div class="summary-card savings-card">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">Active Positions</h3>
                    <p class="card-value">0</p>
                </div>
            </div>

            <div class="summary-card">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                </div>
                <div class="card-content">
                    <h3 class="card-label">P&L</h3>
                    <p class="card-value">$0.00</p>
                </div>
            </div>
        </div>

        <div class="intro-text mb-6">
            <p class="text-sm opacity-75">
                Track and manage all your investment transactions across multiple brokerages. Filter, search, and analyze your trading activity.
            </p>
        </div>

        <!-- Enhanced Filters -->
        <div class="form-section">
            <h2 class="section-title">Filters</h2>
            <form class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="form-field">
                    <label for="brokerage" class="block text-sm font-medium text-gray-700 mb-1">Brokerage</label>
                    <select id="brokerage" name="brokerage" class="form-input">
                        <option value="">All Brokerages</option>
                        {% for brokerage in brokerages %}
                            <option value="{{ brokerage.name }}" {% if request.GET.brokerage == brokerage.name %}selected{% endif %}>{{ brokerage.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-field">
                    <label for="asset_ticker" class="block text-sm font-medium text-gray-700 mb-1">Asset Ticker</label>
                    <input type="text" id="asset_ticker" name="asset_ticker" value="{{ request.GET.asset_ticker }}" placeholder="e.g., AAPL" class="form-input">
                </div>
                <div class="form-field">
                    <label for="owner_name" class="block text-sm font-medium text-gray-700 mb-1">Owner</label>
                    <select id="owner_name" name="owner_name" class="form-input">
                        <option value="">All Owners</option>
                        {% for owner in owners %}
                            <option value="{{ owner.name }}" {% if request.GET.owner_name == owner.name %}selected{% endif %}>{{ owner.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-field">
                    <label for="asset_class" class="block text-sm font-medium text-gray-700 mb-1">Asset Class</label>
                    <select id="asset_class" name="asset_class" class="form-input">
                        <option value="">All Classes</option>
                        {% for class in asset_classes %}
                            <option value="{{ class }}" {% if request.GET.asset_class == class %}selected{% endif %}>{{ class }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-field">
                    <label for="action" class="block text-sm font-medium text-gray-700 mb-1">Action</label>
                    <select id="action" name="action" class="form-input">
                        <option value="">All Actions</option>
                        {% for act, label in actions %}
                            <option value="{{ act }}" {% if request.GET.action == act %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-field">
                    <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                    <select id="currency" name="currency" class="form-input">
                        <option value="">All Currencies</option>
                        {% for currency in currencies %}
                            <option value="{{ currency.code }}" {% if request.GET.currency == currency.code %}selected{% endif %}>{{ currency.code }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-field">
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="date" id="start_date" name="start_date" value="{{ request.GET.start_date }}" class="form-input">
                </div>
                <div class="form-field">
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="date" id="end_date" name="end_date" value="{{ request.GET.end_date }}" class="form-input">
                </div>
                <div class="form-field flex items-end">
                    <button type="submit" class="btn-save w-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Action Buttons -->
        <div class="form-section">
            <div class="flex justify-between items-center mb-4">
                <h2 class="section-title">Transactions</h2>
                <div class="flex space-x-3">
                    <a href="{% url 'transaction_create' %}" class="btn-save">
                        <svg class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Add Transaction
                    </a>
                    <button type="button" class="btn-secondary" onclick="exportTransactions()">
                        <svg class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Export
                    </button>
                </div>
            </div>

            <!-- Transactions Table -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Asset</th>
                            <th>Action</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Total Value</th>
                            <th>Fees</th>
                            <th>Brokerage</th>
                            <th>Owners</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.date|date:"M d, Y" }}</td>
                                <td>
                                    <div class="flex items-center">
                                        <span class="font-medium">{{ transaction.asset.ticker }}</span>
                                        <span class="text-sm text-gray-500 ml-2">{{ transaction.asset.name|truncatechars:30 }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="action-badge action-{{ transaction.action|lower }}">
                                        {{ transaction.get_action_display }}
                                    </span>
                                </td>
                                <td class="text-right">{{ transaction.quantity|floatformat:4 }}</td>
                                <td class="text-right">${{ transaction.price|floatformat:2 }}</td>
                                <td class="text-right">
                                    <span class="{% if transaction.action == 'BUY' or transaction.action == 'COVER' %}text-red-600{% else %}text-green-600{% endif %}">
                                        ${{ transaction.total_cost|floatformat:2 }}
                                    </span>
                                </td>
                                <td class="text-right">${{ transaction.fees|floatformat:2 }}</td>
                                <td>{{ transaction.brokerage.name }}</td>
                                <td>
                                    <div class="text-sm">
                                        {% for ownership in transaction.ownerships.all %}
                                            <div>{{ ownership.owner.name }} ({{ ownership.percentage }}%)</div>
                                        {% empty %}
                                            <span class="text-gray-400">None</span>
                                        {% endfor %}
                                    </div>
                                </td>
                                <td>
                                    <div class="flex space-x-2">
                                        <a href="{% url 'transaction_update' transaction.pk %}" class="btn-edit" title="Edit">
                                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                        </a>
                                        <a href="{% url 'transaction_delete' transaction.pk %}" class="btn-delete" title="Delete" onclick="return confirm('Are you sure you want to delete this transaction?')">
                                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="10" class="text-center py-8 text-gray-500">
                                    <div class="flex flex-col items-center">
                                        <svg class="h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <p class="text-lg font-medium">No transactions found</p>
                                        <p class="text-sm">Start by adding your first investment transaction.</p>
                                        <a href="{% url 'transaction_create' %}" class="btn-save mt-4">Add Transaction</a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
            </tbody>
        </table>
    </div>

        <!-- Owner Totals Summary -->
        {% if owner_totals %}
        <div class="form-section">
            <h2 class="section-title">Owner Summary</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                {% for owner, total in owner_totals.items %}
                    <div class="summary-card">
                        <div class="icon-container">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div class="card-content">
                            <h3 class="card-label">{{ owner }}</h3>
                            <p class="card-value {% if total >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                ${{ total|floatformat:2 }}
                            </p>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="form-section">
            <nav class="flex justify-between items-center">
                <div>
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="btn-secondary">Previous</a>
                    {% endif %}
                </div>
                <div class="text-sm text-gray-600">
                    Page {{ page_obj.number }} of {{ paginator.num_pages }}
                </div>
                <div>
                    {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="btn-secondary">Next</a>
                    {% endif %}
                </div>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

{% endblock investments_content %}

{% block investments_js %}
<script>
function exportTransactions() {
    // Get current filter parameters
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');

    // Create download link
    const exportUrl = window.location.pathname + '?' + params.toString();
    window.open(exportUrl, '_blank');
}

// Add action badge styling
document.addEventListener('DOMContentLoaded', function() {
    const actionBadges = document.querySelectorAll('.action-badge');
    actionBadges.forEach(badge => {
        const action = badge.textContent.trim().toLowerCase();
        badge.classList.add(`action-${action}`);
    });
});
</script>
{% endblock investments_js %}