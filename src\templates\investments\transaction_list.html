{% extends "dashboard/base.html" %}
{% load static %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-4">Transaction List</h1>

    <!-- Enhanced Filters -->
    <form class="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
            <label for="brokerage" class="block text-sm font-medium text-gray-700">Brokerage</label>
            <select id="brokerage" name="brokerage" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                <option value="">All</option>
                {% for brokerage in brokerages %}
                    <option value="{{ brokerage.name }}" {% if request.GET.brokerage == brokerage.name %}selected{% endif %}>{{ brokerage.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div>
            <label for="asset_ticker" class="block text-sm font-medium text-gray-700">Asset Ticker</label>
            <input type="text" id="asset_ticker" name="asset_ticker" value="{{ request.GET.asset_ticker }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        </div>
        <div>
            <label for="owner_name" class="block text-sm font-medium text-gray-700">Owner</label>
            <select id="owner_name" name="owner_name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                <option value="">All</option>
                {% for owner in owners %}
                    <option value="{{ owner.name }}" {% if request.GET.owner_name == owner.name %}selected{% endif %}>{{ owner.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div>
            <label for="asset_class" class="block text-sm font-medium text-gray-700">Asset Class</label>
            <select id="asset_class" name="asset_class" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                <option value="">All</option>
                {% for class in asset_classes %}
                    <option value="{{ class }}" {% if request.GET.asset_class == class %}selected{% endif %}>{{ class }}</option>
                {% endfor %}
            </select>
        </div>
        <div>
            <label for="action" class="block text-sm font-medium text-gray-700">Action</label>
            <select id="action" name="action" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                <option value="">All</option>
                {% for act, label in actions %}
                    <option value="{{ act }}" {% if request.GET.action == act %}selected{% endif %}>{{ label }}</option>
                {% endfor %}
            </select>
        </div>
        <div>
            <label for="currency" class="block text-sm font-medium text-gray-700">Currency</label>
            <select id="currency" name="currency" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                <option value="">All</option>
                {% for currency in currencies %}
                    <option value="{{ currency.code }}" {% if request.GET.currency == currency.code %}selected{% endif %}>{{ currency.code }}</option>
                {% endfor %}
            </select>
        </div>
        <div>
            <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
            <input type="date" id="start_date" name="start_date" value="{{ request.GET.start_date }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        </div>
        <div>
            <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
            <input type="date" id="end_date" name="end_date" value="{{ request.GET.end_date }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        </div>
        <div class="flex items-end">
            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">Filter</button>
        </div>
    </form>

    <!-- Add Transaction Button -->
    <a href="{% url 'transaction_create' %}" class="mb-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700">Add Transaction</a>

    <!-- Transaction Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Brokerage</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fees</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Currency</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owners</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for transaction in transactions %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">{{ transaction.brokerage.name }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ transaction.asset.ticker }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ transaction.date|date:"Y-m-d" }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ transaction.action }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ transaction.quantity }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ transaction.price }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ transaction.fees }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ transaction.currency.code }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% for ownership in transaction.ownerships.all %}
                            {{ ownership.owner.name }} ({{ ownership.percentage }}%)<br>
                        {% empty %}
                            None
                        {% endfor %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <a href="{% url 'finance:transaction_update' transaction.pk %}" class="text-indigo-600 hover:text-indigo-900 mr-2">Edit</a>
                        <a href="{% url 'finance:transaction_delete' transaction.pk %}" class="text-red-600 hover:text-red-900">Delete</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="10" class="px-6 py-4 text-center text-gray-500">No transactions found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Owner Totals -->
    <div class="mt-6 p-4 bg-gray-100 rounded-md">
        <h2 class="text-lg font-semibold text-gray-900">Owner Totals (Approximate P/L)</h2>
        {% for owner, total in owner_totals.items %}
            <p>{{ owner }}: {{ total|floatformat:2 }} {{ transaction.currency.code }}</p>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="mt-6">
        <nav class="flex justify-between items-center">
            <div>
                {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                {% endif %}
            </div>
            <div>
                Page {{ page_obj.number }} of {{ paginator.num_pages }}
            </div>
            <div>
                {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                {% endif %}
            </div>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}