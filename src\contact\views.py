from django.shortcuts import render
from visits.models import PageVisit
from django.core.mail import send_mail
from django.contrib import messages
from .models import ContactMessage
from django.conf import settings
from django.contrib.auth import get_user_model

User = get_user_model()

def contact_view(request):
    page_title = "Contact"
    qs = PageVisit.objects.all()
    PageVisit.objects.create(path=request.path)
    if request.method == "POST":
        message_name = request.POST.get('name')
        message_email = request.POST.get('email')
        message_subject = request.POST.get('subject')
        message = request.POST.get('message')
        send_mail(
            f"CF - {message_name} {message_subject}", # subject
            f"Email: {message_email} \nMessasge: {message}", # message
            settings.DEFAULT_FROM_EMAIL, # from email
            ['<EMAIL>', ], # recipient list
        )

        contact_message = ContactMessage(
            name=message_name,
            email=message_email,
            subject=message_subject,
            message=message,
        )

        if request.user.is_authenticated:
            contact_message.user = request.user
            contact_message.is_user = True
        else:
            user = User.objects.filter(email=message_email).first()
            if user:
                contact_message.user = user
                contact_message.is_user = True

        contact_message.save()

        subject = f"We've received your message: PennyMize"
        send_mail(
            subject,
            f"Subject: {message_subject} \nMessasge: {message}",
            settings.DEFAULT_FROM_EMAIL,
            [message_email],
            fail_silently=False,
        )

        messages.success(request, "Your message has been sent. We'll get back to you soon!")

        return render(request, "contact/main.html", {"page_title": page_title, "message_name": message_name})
            
    return render(request, "contact/main.html", {"page_title": page_title,
                                                 "page_visit_count": qs.count()})
