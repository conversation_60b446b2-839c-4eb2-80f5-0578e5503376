from django.core.management.base import BaseCommand
from subscriptions.models import Subscription

class Command(BaseCommand):
    help = 'Sync Subscriptions with Groups and Permissions'

    def handle(self, *args, **options):
        qs = Subscription.objects.filter(active=True)
        for obj in qs:
            # print(obj.groups.all())
            # print(obj.permissions.all())
            sub_perms = obj.permissions.all()
            for group in obj.groups.all():
                # for perm in obj.permissions.all():
                #     group.permissions.add(perm) # this won't remove extra permission which added mistakenly
                group.permissions.set(sub_perms)