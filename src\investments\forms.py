from django import forms
from django.core.exceptions import ValidationError
from decimal import Decimal
from .models import (
    Transaction, Asset, Brokerage, Currency, Owner, 
    TransactionTypeChoices, AssetClassChoices, AssetTypeChoices
)


class TransactionForm(forms.ModelForm):
    class Meta:
        model = Transaction
        fields = [
            'brokerage', 'asset', 'date', 'action', 'quantity', 'price',
            'fees', 'commission', 'sec_fee', 'other_fees', 'currency',
            'order_type', 'execution_time', 'notes', 'trade_reason', 'is_short'
        ]
        widgets = {
            'date': forms.DateTimeInput(
                attrs={
                    'type': 'datetime-local',
                    'class': 'form-input'
                }
            ),
            'brokerage': forms.Select(attrs={'class': 'form-input'}),
            'asset': forms.Select(attrs={'class': 'form-input'}),
            'action': forms.Select(attrs={'class': 'form-input'}),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.0001',
                'min': '0'
            }),
            'price': forms.NumberInput(attrs={
                'class': 'form-input pl-8',
                'step': '0.01',
                'min': '0'
            }),
            'fees': forms.NumberInput(attrs={
                'class': 'form-input pl-8',
                'step': '0.01',
                'min': '0',
                'value': '0'
            }),
            'commission': forms.NumberInput(attrs={
                'class': 'form-input pl-8',
                'step': '0.01',
                'min': '0',
                'value': '0'
            }),
            'sec_fee': forms.NumberInput(attrs={
                'class': 'form-input pl-8',
                'step': '0.01',
                'min': '0',
                'value': '0'
            }),
            'other_fees': forms.NumberInput(attrs={
                'class': 'form-input pl-8',
                'step': '0.01',
                'min': '0',
                'value': '0'
            }),
            'currency': forms.Select(attrs={'class': 'form-input'}),
            'order_type': forms.Select(attrs={'class': 'form-input'}),
            'execution_time': forms.TimeInput(attrs={
                'type': 'time',
                'class': 'form-input'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-input',
                'rows': 3,
                'placeholder': 'Additional notes about this transaction...'
            }),
            'trade_reason': forms.Textarea(attrs={
                'class': 'form-input',
                'rows': 2,
                'placeholder': 'Why did you make this trade?'
            }),
            'is_short': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set initial values
        if not self.instance.pk:
            self.fields['fees'].initial = 0
            self.fields['commission'].initial = 0
            self.fields['sec_fee'].initial = 0
            self.fields['other_fees'].initial = 0
        
        # Filter active brokerages and currencies
        self.fields['brokerage'].queryset = Brokerage.objects.filter(is_active=True)
        self.fields['currency'].queryset = Currency.objects.all()
        self.fields['asset'].queryset = Asset.objects.filter(is_active=True)

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity <= 0:
            raise ValidationError("Quantity must be greater than zero.")
        return quantity

    def clean_price(self):
        price = self.cleaned_data.get('price')
        if price is not None and price <= 0:
            raise ValidationError("Price must be greater than zero.")
        return price

    def clean(self):
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        asset = cleaned_data.get('asset')
        
        # Validate option-specific fields
        if asset and asset.asset_type == 'OPTION':
            if not asset.option_type or not asset.strike_price or not asset.expiration_date:
                raise ValidationError(
                    "Option assets must have option type, strike price, and expiration date."
                )
        
        return cleaned_data


class AssetForm(forms.ModelForm):
    class Meta:
        model = Asset
        fields = [
            'ticker', 'name', 'asset_class', 'asset_type', 'sector', 'industry',
            'country', 'exchange', 'currency', 'underlying_asset', 'option_type',
            'strike_price', 'expiration_date', 'contract_size'
        ]
        widgets = {
            'ticker': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'e.g., AAPL'
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'e.g., Apple Inc.'
            }),
            'asset_class': forms.Select(attrs={'class': 'form-input'}),
            'asset_type': forms.Select(attrs={'class': 'form-input'}),
            'sector': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'e.g., Technology'
            }),
            'industry': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'e.g., Software'
            }),
            'country': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'e.g., United States'
            }),
            'exchange': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'e.g., NASDAQ'
            }),
            'currency': forms.Select(attrs={'class': 'form-input'}),
            'underlying_asset': forms.Select(attrs={'class': 'form-input'}),
            'option_type': forms.Select(attrs={'class': 'form-input'}),
            'strike_price': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.01'
            }),
            'expiration_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-input'
            }),
            'contract_size': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.01'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Make fields conditional based on asset type
        self.fields['underlying_asset'].required = False
        self.fields['option_type'].required = False
        self.fields['strike_price'].required = False
        self.fields['expiration_date'].required = False
        self.fields['contract_size'].required = False

    def clean(self):
        cleaned_data = super().clean()
        asset_type = cleaned_data.get('asset_type')
        
        # Validate option-specific fields
        if asset_type == 'OPTION':
            required_fields = ['underlying_asset', 'option_type', 'strike_price', 'expiration_date']
            for field in required_fields:
                if not cleaned_data.get(field):
                    self.add_error(field, f"This field is required for option assets.")
        
        # Validate futures-specific fields
        if asset_type == 'FUTURE':
            if not cleaned_data.get('contract_size'):
                self.add_error('contract_size', "Contract size is required for futures.")
        
        return cleaned_data


class BrokerageForm(forms.ModelForm):
    class Meta:
        model = Brokerage
        fields = ['name', 'account_number']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'e.g., Interactive Brokers'
            }),
            'account_number': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Account number (optional)'
            }),
        }


class OwnerForm(forms.ModelForm):
    class Meta:
        model = Owner
        fields = ['name', 'email', 'tax_id', 'default_currency']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Full name'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-input',
                'placeholder': '<EMAIL>'
            }),
            'tax_id': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'SSN, SIN, etc. (optional)'
            }),
            'default_currency': forms.Select(attrs={'class': 'form-input'}),
        }


class TransactionFilterForm(forms.Form):
    brokerage = forms.ModelChoiceField(
        queryset=Brokerage.objects.filter(is_active=True),
        required=False,
        empty_label="All Brokerages",
        widget=forms.Select(attrs={'class': 'form-input'})
    )
    asset_ticker = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'e.g., AAPL'
        })
    )
    owner = forms.ModelChoiceField(
        queryset=Owner.objects.filter(is_active=True),
        required=False,
        empty_label="All Owners",
        widget=forms.Select(attrs={'class': 'form-input'})
    )
    asset_class = forms.ChoiceField(
        choices=[('', 'All Classes')] + AssetClassChoices.choices,
        required=False,
        widget=forms.Select(attrs={'class': 'form-input'})
    )
    action = forms.ChoiceField(
        choices=[('', 'All Actions')] + TransactionTypeChoices.choices,
        required=False,
        widget=forms.Select(attrs={'class': 'form-input'})
    )
    currency = forms.ModelChoiceField(
        queryset=Currency.objects.all(),
        required=False,
        empty_label="All Currencies",
        widget=forms.Select(attrs={'class': 'form-input'})
    )
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input'
        })
    )
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input'
        })
    )
