{% extends "dashboard/base.html" %}
{% load static %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-4">Portfolio Dashboard</h1>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold text-gray-900">Total Portfolio Value</h2>
            <p class="text-2xl font-bold text-indigo-600">{{ total_value|floatformat:2 }} CAD</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold text-gray-900">Unrealized P/L</h2>
            <p class="text-2xl font-bold {% if unrealized_pl >= 0 %}text-green-600{% else %}text-red-600{% endif %}">{{ unrealized_pl|floatformat:2 }} CAD</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold text-gray-900">ROI</h2>
            <p class="text-2xl font-bold {% if roi >= 0 %}text-green-600{% else %}text-red-600{% endif %}">{{ roi }}%</p>
        </div>
    </div>

    <!-- Portfolio Table -->
    <div class="overflow-x-auto mb-6">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticker</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Purchase Price</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Price</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value (CAD)</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for item in portfolio %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">{{ item.asset_name }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ item.ticker }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ item.quantity }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ item.avg_purchase_price }} {{ item.currency.code }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ item.current_price }} {{ item.currency.code }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {{ item.total_value_cad|floatformat:2 }} CAD
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">No portfolio items found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Platform Breakdown -->
    <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Platform Breakdown</h2>
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Platform</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Deposits</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Profits</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for platform in platform_breakdown %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">{{ platform.location__name }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ platform.total_deposit|floatformat:2|default:0 }} CAD</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ platform.total_profit|floatformat:2|default:0 }} CAD</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}