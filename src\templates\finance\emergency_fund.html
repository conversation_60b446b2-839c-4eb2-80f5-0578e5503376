{% extends 'finance/base_finance.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block finance_content %}
<div class="finance-form-container">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6 text-primary">Emergency Fund</h1>

        <div class="intro-text mb-6">
            <p class="text-sm opacity-75">
                Your emergency fund is a safety net for unexpected expenses. Financial experts recommend having 3-6 months of essential expenses saved.
            </p>
        </div>
        
        <!-- Summary Card -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <h3 class="text-lg font-medium mb-3">Current Status</h3>
                <p class="mb-2">Current Amount: <span class="font-semibold">${{ emergency_fund.current_amount }}</span></p>
                <p>Target Amount: <span class="font-semibold">${{ emergency_fund.emergency_fund_target }}</span></p>
            </div>
            <div>
                <h3 class="text-lg font-medium mb-3">Progress</h3>
                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                    <div class="bg-green-500 h-4 rounded-full" style="width: {{ progress_percentage }}%"></div>
                </div>
                <p class="text-sm text-right">{{ progress_percentage }}% of goal</p>
            </div>
        </div>
        
        <!-- Fund Target Form -->
        <div class="finance-form mb-6">
            <div class="mt-4">
                <form id="fund-settings-form" method="POST" class="space-y-4">
                    {% csrf_token %}
                    <input type="hidden" name="update_fund" value="1">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-field">
                            {{ fund_form.emergency_fund_target|as_crispy_field }}
                        </div>
                        <div class="field-info flex items-center">
                            <p>Your target emergency fund amount</p>
                        </div>
                        <div class="form-field">
                            {{ fund_form.current_amount|as_crispy_field }}
                        </div>
                        <div class="field-info flex items-center">
                            <p>Currently saved in your emergency fund</p>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn-save mt-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        Update
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Monthly Payment Form -->
        <div class="finance-form mb-6">
            <h3 class="section-title mb-4">Add New Contribution</h3>
            <form id="contribution-form" method="POST" class="space-y-4">
                {% csrf_token %}
                <input type="hidden" name="add_contribution" value="1">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-field">
                        {{ contribution_form.contribution_amount|as_crispy_field }}
                    </div>
                    <div class="form-field">
                        {{ contribution_form.contribution_date|as_crispy_field }}
                    </div>
                </div>
                
                <div class="form-field">
                    {{ contribution_form.notes|as_crispy_field }}
                </div>
                
                <button type="submit" class="btn-save">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    Add Contribution
                </button>
            </form>
        </div>
        
        <!-- Contribution History -->
        <div class="mt-8">
            <h3 class="section-title mb-4">Contribution History</h3>
            {% if contributions %}
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-gray-800 rounded-lg overflow-hidden">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Amount</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Notes</th>
                                <th class="px-4 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            {% for contribution in contributions %}
                                <tr>
                                    <td class="px-4 py-3 whitespace-nowrap">{{ contribution.contribution_date }}</td>
                                    <td class="px-4 py-3 whitespace-nowrap">${{ contribution.contribution_amount }}</td>
                                    <td class="px-4 py-3">{{ contribution.notes|default:"-" }}</td>
                                    <td class="px-4 py-3 text-center">
                                        <button type="button" class="edit-contribution btn-secondary text-xs px-2 py-1 mr-1" data-id="{{ contribution.id }}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                            </svg>
                                            Edit
                                        </button>
                                        <button type="button" class="delete-contribution btn-danger text-xs px-2 py-1" data-id="{{ contribution.id }}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                            Delete
                                        </button>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-gray-400">No contributions yet. Start building your emergency fund today!</p>
            {% endif %}
        </div>
        
        <!-- Navigation Buttons -->
        <div class="navigation-buttons mt-12 mb-6">
            <button id="previous-button" class="nav-button prev-button">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Monthly Overview
            </button>
            <button id="next-button" class="nav-button next-button">
                Debt Management
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        </div>
    </div>
</div>

<div class="loading-indicator" style="display: none;">
    <div class="spinner"></div>
</div>

{% block extra_content %}
{% include 'base/loading_spinner.html' %}
{% include 'base/popup_modal.html' %}
{% endblock extra_content %}

<!-- Edit Contribution Modal -->
<div id="editContributionModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full flex justify-center items-center">
    <div class="relative w-full max-w-md max-h-full">
        <div class="relative bg-gray-800 rounded-lg shadow">
            <div class="flex items-center justify-between p-4 border-b border-gray-700">
                <h3 class="text-xl font-semibold text-white">
                    Edit Contribution
                </h3>
                <button type="button" id="close-edit-modal" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form id="edit-contribution-form" class="p-4 space-y-4">
                {% csrf_token %}
                <input type="hidden" id="edit-contribution-id" name="contribution_id">
                
                <div class="form-field">
                    <label for="edit-contribution-amount" class="block mb-2 text-sm font-medium text-white">Amount</label>
                    <input type="number" id="edit-contribution-amount" name="contribution_amount" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" step="0.01" required>
                </div>
                
                <div class="form-field">
                    <label for="edit-contribution-date" class="block mb-2 text-sm font-medium text-white">Date</label>
                    <input type="date" id="edit-contribution-date" name="contribution_date" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" required>
                </div>
                
                <div class="form-field">
                    <label for="edit-contribution-notes" class="block mb-2 text-sm font-medium text-white">Notes</label>
                    <textarea id="edit-contribution-notes" name="notes" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" rows="3"></textarea>
                </div>
                
                <div class="flex justify-end pt-2">
                    <button type="button" id="cancel-edit-btn" class="btn-secondary mr-2">Cancel</button>
                    <button type="submit" class="btn-save">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/alerts.js' %}"></script>
<script src="{% static 'finance/finance.js' %}"></script>
<script src="{% static 'finance/emergency_fund.js' %}"></script>
{% endblock %}
