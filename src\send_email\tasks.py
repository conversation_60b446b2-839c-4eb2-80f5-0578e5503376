from django.contrib.auth.models import User
from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from datetime import timedelta

@shared_task(bind=True)
def send_welcome_email(self, user_id):
    """
    Send welcome email to verified user on signup
    """
    user = User.objects.get(id=user_id)
    mail_subject = "Welcome to PennyMize"
    mail_message = "Thank you for signing up to PennyMize. We hope you enjoy your stay here."
    to_email = user.email
    send_mail(
        subject=mail_subject,
        message=mail_message,
        from_email=settings.DEFAULT_FROM_EMAIL,
        recipient_list=[to_email],
        fail_silently=False
    )
    # Update profile to indicate email has been sent
    if hasattr(user, 'profile'):
        profile = user.profile
        profile.welcome_email_sent = True
        profile.save()

@shared_task(bind=True)
def send_flogin_feedback_email(self, user_id):
    """
    Send feedback email to user after first login
    """
    user = User.objects.get(id=user_id)
    mail_subject = "PennyMize - We value your feedback"
    mail_message = "Please share your experience with us."
    send_mail(
        subject=mail_subject,
        message=mail_message,
        from_email='<EMAIL>',
        recipient_list=[user.email],
        fail_silently=False,
    )
    if hasattr(user, 'profile'):
        profile = user.profile
        profile.flogin_email_sent = True
        profile.save()

@shared_task
def schedule_feedback_emails():
    users = User.objects.filter(last_login__gte=timezone.now() - timedelta(days=1), profile__flogin_email_sent=False)
    for user in users:
        send_flogin_feedback_email.delay(user.id)