from django.contrib.auth import authenticate, login
from django.shortcuts import render, redirect

from django.contrib.auth import get_user_model

User = get_user_model()

def login_view(request):
    if request.method == "POST":
        username = request.POST.get("username") or None
        password = request.POST.get("password") or None
        if all([username, password]):
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                print("user logged in")
                return redirect("/")
            #     return render(request, "home.html", {})
            # else:
            #     return render(request, "auth/login.html", {})
    return render(request, "auth/login.html", {})

def register_view(request):
    if request.method == "POST":
        username = request.POST.get("username") or None
        email = request.POST.get("email") or None
        password = request.POST.get("password") or None

        # username_exits = User.objects.filter(username__iexact=username).exists()
        # email_exits = User.objects.filter(email__iexact=email).exists()
        try:
            User.objects.create_user(username=username, password=password)
            print("user created")
        except Exception as e:
            print(e)

    return render(request, "auth/register.html", {})