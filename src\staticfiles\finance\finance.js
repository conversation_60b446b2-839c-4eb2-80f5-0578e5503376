var FinanceForm = FinanceForm || {};

FinanceForm.common = (function() {
    let formModified = false;

    function initCommon() {
        // Restrict negative & special input in decimal fields
        const decimalFields = document.querySelectorAll('input[type="number"][step="0.01"]');
        decimalFields.forEach(field => {
            field.addEventListener('input', validateDecimalInput);
        });

        // Attach event listeners to buttons
        const previousButton = document.getElementById("previous-button");
        const submitButton = document.getElementById("submit-button");
        const nextButton = document.getElementById("next-button");

        if (previousButton) previousButton.addEventListener('click', (e) => showConfirmationDialog(e, 'previous'));
        if (submitButton) submitButton.addEventListener('click', (e) => showConfirmationDialog(e, 'save'));
        if (nextButton) nextButton.addEventListener('click', (e) => showConfirmationDialog(e, 'next'));

        // Mark form as modified when any input changes
        document.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('change', () => { formModified = true; });
        });
    }

    function validateDecimalInput() {
        // Check for negative numbers or non-numeric input
        if (parseFloat(this.value) < 0) {
            this.value = '0';
            alert('Please enter a positive number.');
            event.preventDefault();
            return;
        }
        // Check for special characters and mathematical signs
        const validInput = /^[0-9.]+$/;
        if (!validInput.test(this.value)) {
            this.value = '0';
            alert('Please enter a valid positive number.');
            event.preventDefault();
            return;
        }
    }

    function resetFormModified() {
        formModified = false;
    }

    function getCurrentModule() {
        const pathMapping = {
            '/finance/monthly-overview/': 'monthly-overview',
            '/finance/emergency-fund/': 'emergency-fund',
            '/finance/debt-management/': 'debt-management',
            '/finance/employer-benefits/': 'employer-benefits',
            '/finance/investment-accounts/': 'investment-accounts',
            '/finance/financial-goals/': 'financial-goals'
        };
        
        const currentPath = window.location.pathname;
        return pathMapping[currentPath] || null;
    }

    function generateNavigationUrl(direction) {
        const moduleOrder = [
            'monthly-overview',
            'emergency-fund',
            'debt-management',
            'retirement-planning',
            'investment-strategy',
            'financial-goals'
        ];

        const currentModule = getCurrentModule();
        const currentIndex = moduleOrder.indexOf(currentModule);
        
        if (currentIndex === -1) return '/';

        let targetIndex;
        if (direction === 'previous') {
            targetIndex = currentIndex - 1;
            if (targetIndex < 0) return '/';
        } else {
            targetIndex = currentIndex + 1;
            if (targetIndex >= moduleOrder.length) return '/';
        }

        const targetModule = moduleOrder[targetIndex];
        const month = document.getElementById('id_month')?.value;
        const year = document.getElementById('id_year')?.value;
        
        const baseUrls = {
            'monthly-overview': '/finance/monthly-overview/',
            'emergency-fund': '/finance/emergency-fund/',
            'debt-management': '/finance/debt-management/',
            'employer-benefits': '/finance/employer-benefits/',
            'investment-accounts': '/finance/investment-accounts/',
            'financial-goals': '/finance/financial-goals/'
        };

        let url = baseUrls[targetModule];
        
        // Add month and year as query parameters if they exist
        if (month && year) {
            url += `?month=${month}&year=${year}`;
        }

        return url;
    }

    function showConfirmationDialog(event, buttonType) {
        // Get the current module
        const currentModule = getCurrentModule();
        
        // Check if we're on the monthly overview page and if there's no data for the selected month
        const isMonthlyOverview = currentModule === 'monthly-overview';
        const messageElement = document.getElementById('message-container');
        const isNoDataMonth = messageElement && messageElement.textContent.includes('Data not available for the selected month-year');
        
        // Only show confirmation if form was actually modified by user input
        // Don't show confirmation if there's no data and user hasn't entered anything
        if (formModified && !(isMonthlyOverview && isNoDataMonth && !userEnteredData())) {
            event.preventDefault();
            const modal = document.getElementById('popup-modal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');

            const confirmButton = document.getElementById('confirm-leave-button');
            const cancelButton = document.getElementById('cancel-leave-button');
            const closeButton = document.getElementById('close-modal-button');

            const hideModal = () => {
                modal.classList.add('hidden');
                modal.classList.remove('flex');
            };

            confirmButton.onclick = () => {
                hideModal();
                // Save the form data first, then navigate if needed
                performAction(event, buttonType);
            };

            cancelButton.onclick = hideModal;
            closeButton.onclick = hideModal;
        } else {
            performAction(event, buttonType, true);
        }
    }

    // Helper function to check if user has entered any data in the form
    function userEnteredData() {
        // Get all input fields
        const inputs = document.querySelectorAll('input[type="text"], input[type="number"], textarea, select');
        
        // Check if any input has a value
        for (const input of inputs) {
            // Skip hidden inputs and fields that might be pre-filled
            if (input.type === 'hidden' || input.id === 'id_month' || input.id === 'id_year') {
                continue;
            }
            
            // If any field has a value, user has entered data
            if (input.value && input.value !== '0' && input.value !== '0.00') {
                return true;
            }
        }
        
        // No user-entered data found
        return false;
    }

    function performAction(event, buttonType, isCancel = false) {
        // Get the current module
        const currentModule = getCurrentModule();
        
        // Check if we're on the monthly overview page and if there's no data for the selected month
        const isMonthlyOverview = currentModule === 'monthly-overview';
        const messageElement = document.getElementById('message-container');
        const isNoDataMonth = messageElement && messageElement.textContent.includes('Data not available for the selected month-year');
        
        // If we're on monthly overview with no data and user hasn't entered anything,
        // just navigate without trying to save
        if (isMonthlyOverview && isNoDataMonth && !userEnteredData() && !isCancel) {
            if (buttonType === 'previous') {
                window.location.href = generateNavigationUrl('previous');
                return;
            } else if (buttonType === 'next') {
                window.location.href = generateNavigationUrl('next');
                return;
            } else if (buttonType === 'save') {
                window.location.href = '/';
                return;
            }
        }
        
        switch (buttonType) {
            case 'previous':
                if (isCancel) {
                    window.location.href = generateNavigationUrl('previous');
                } else {
                    customValidationAndSubmit(event);
                }
                break;
            case 'save':
                if (isCancel) {
                    window.location.href = '/';
                } else {
                    customValidationAndSubmit(event);
                }
                break;
            case 'next':
                if (isCancel) {
                    window.location.href = generateNavigationUrl('next');
                } else {
                    customValidationAndSubmit(event);
                }
                break;
        }
    }

    function customValidationAndSubmit(event) {
        const currentModule = getCurrentModule();
        const moduleMap = {
            'monthly-overview': FinanceForm.MonthlyOverview,
            'emergency-fund': FinanceForm.EmergencyFund,
            'debt-management': FinanceForm.DebtManagement,
            'employer-benefits': FinanceForm.EmployerBenefits,
            'investment-accounts': FinanceForm.InvestmentAccounts,
            'financial-goals': FinanceForm.FinancialGoals
        };

        const currentModuleObj = moduleMap[currentModule];
        
        if (currentModuleObj && typeof currentModuleObj.customValidationAndSubmit === 'function') {
            currentModuleObj.customValidationAndSubmit(event);
        } else {
            console.warn(`customValidationAndSubmit not found for ${currentModule}`);
            const form = document.querySelector('form');
            if (form) form.submit();
        }
    }

    return {
        init: initCommon,
        performAction: performAction,
        showConfirmationDialog: showConfirmationDialog,
        customValidationAndSubmit: customValidationAndSubmit,
        resetFormModified: () => resetFormModified(),
        isFormModified: () => formModified
    };
})();

document.addEventListener("DOMContentLoaded", FinanceForm.common.init);
