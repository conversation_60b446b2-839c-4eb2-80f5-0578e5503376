{% extends 'dashboard/base.html' %}
{% load crispy_forms_tags %}
{% load static %}

{% block head_title %}{{ page_title }} - {{ block.super }}{% endblock head_title %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'investments/investments.css' %}">
{% endblock extra_css %}

{% block content %}
<div class="finance-page-container">
    <!-- Investment Navigation Tabs -->
    <div class="finance-navbar">
        <div class="finance-tabs-container">
            <a href="{% url 'transaction_list' %}" class="finance-tab {% if active_step == 'transactions' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>                  
                <span>Transactions</span>
            </a>
            
            <a href="{% url 'portfolio_overview' %}" class="finance-tab {% if active_step == 'portfolio' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <polyline points="23 6 13.5 15.5 8.5 10.5 1 18" />
                    <polyline points="17 6 23 6 23 12" />
                </svg>                  
                <span>Portfolio</span>
            </a>
            
            <a href="{% url 'positions_list' %}" class="finance-tab {% if active_step == 'positions' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                    <line x1="8" y1="21" x2="16" y2="21" />
                    <line x1="12" y1="17" x2="12" y2="21" />
                </svg>                  
                <span>Positions</span>
            </a>
            
            <a href="{% url 'dividends_list' %}" class="finance-tab {% if active_step == 'dividends' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <path d="M12 2l8 4v6c0 5.25-3.6 9.33-8 10-4.4-.67-8-4.75-8-10V6l8-4z" />
                    <circle cx="12" cy="12" r="3" />
                </svg>                  
                <span>Dividends</span>
            </a>
            
            <a href="{% url 'performance_analytics' %}" class="finance-tab {% if active_step == 'analytics' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <path d="M3 3v18h18" />
                    <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
                </svg>
                <span>Analytics</span>
            </a>
            
            <a href="{% url 'tax_lots_list' %}" class="finance-tab {% if active_step == 'tax_lots' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                    <polyline points="14,2 14,8 20,8" />
                    <line x1="16" y1="13" x2="8" y2="13" />
                    <line x1="16" y1="17" x2="8" y2="17" />
                    <polyline points="10,9 9,9 8,9" />
                </svg>
                <span>Tax Lots</span>
            </a>

            <a href="{% url 'alerts_list' %}" class="finance-tab {% if active_step == 'alerts' %}active{% endif %}">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tab-icon">
                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
                    <path d="M13.73 21a2 2 0 0 1-3.46 0" />
                </svg>
                <span>Alerts</span>
            </a>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="finance-content">
        {% block investments_content %}{% endblock %}
    </div>

    {% block extra_content %}
    {% include 'base/loading_spinner.html' %}
    {% include 'base/popup_modal.html' %}
    {% endblock extra_content %}
</div>
{% endblock content %}

{% block extra_js %}
<script src="{% static 'finance/finance.js' %}"></script>
{% block investments_js %}{% endblock %}
<script>
    // Add horizontal scrolling with mousewheel for the tabs
    document.addEventListener('DOMContentLoaded', function() {
        const tabsContainer = document.querySelector('.finance-tabs-container');
        if (tabsContainer) {
            tabsContainer.addEventListener('wheel', function(e) {
                if (e.deltaY !== 0) {
                    e.preventDefault();
                    tabsContainer.scrollLeft += e.deltaY;
                }
            });
        }
    });
</script>
{% endblock extra_js %}
