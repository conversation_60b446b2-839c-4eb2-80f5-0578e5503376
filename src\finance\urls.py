from django.urls import path

from . import views

urlpatterns = [
    path('monthly-overview/', views.monthly_overview, name='finance_monthly_overview'),
    path('emergency-fund/', views.emergency_fund, name='finance_emergency_fund'),
    path('emergency-fund/contribution/<int:contribution_id>/', views.get_emergency_contribution, name='finance_get_emergency_contribution'),
    path('emergency-fund/contribution/<int:contribution_id>/edit/', views.edit_emergency_contribution, name='finance_edit_emergency_contribution'),
    path('emergency-fund/contribution/<int:contribution_id>/delete/', views.delete_emergency_contribution, name='finance_delete_emergency_contribution'),
    path('emergency-fund/transactions/<int:year>/<int:month>/', views.get_emergency_payments, name='finance_get_emergency_payments'),
    path('debt-management/', views.debt_management, name='finance_debt_management'),
    path('debt/create/', views.handle_create_debt, name='create_debt'),
    path('debt/<int:debt_id>/edit/', views.get_debt_detail, name='get_debt_detail'),
    path('debt/<int:debt_id>/update/', views.handle_update_debt, name='update_debt'),
    path('debt/<int:debt_id>/delete/', views.handle_delete_debt, name='delete_debt'),
    path('debt/list/', views.get_debt_list, name='get_debt_list'),
    path('debt/summary/', views.get_debt_summary, name='get_debt_summary'),
    path('payment/add/', views.handle_add_payment, name='add_payment'),
    path('payment/<int:payment_id>/edit/', views.get_payment_detail, name='get_payment_detail'),
    path('payment/<int:payment_id>/update/', views.handle_update_payment, name='update_payment'),
    path('payment/<int:payment_id>/delete/', views.handle_delete_payment, name='delete_payment'),
    path('payments/<int:year>/<int:month>/', views.get_debt_payments, name='get_debt_payments'),
    path('employer-benefits/', views.employer_benefits, name='finance_employer_benefits'),
    path('investment-accounts/', views.investment_accounts, name='finance_investment_accounts'),
    path('financial-goals/', views.financial_goals, name='finance_financial_goals'),
]

