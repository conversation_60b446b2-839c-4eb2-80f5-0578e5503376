# Generated by Django 5.0.2 on 2025-05-04 22:19

import django.db.models.deletion
import finance.models
import helpers.validators
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BasicFinance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('has_debt', models.BooleanField(blank=True, null=True)),
                ('has_employer_match', models.BooleanField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Debt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('initial_amount', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('interest_rate', finance.models.PercentageField(decimal_places=2, max_digits=5, validators=[helpers.validators.number_input_validation])),
                ('minimum_payment', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('planned_payment', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('remaining_balance', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('start_date', models.DateField()),
                ('estimated_end_date', models.DateField(blank=True, null=True)),
                ('is_paid_off', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'name')},
            },
        ),
        migrations.CreateModel(
            name='EmergencyFund',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_amount', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('emergency_fund_target', finance.models.MoneyField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[helpers.validators.number_input_validation])),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='EmployerBenefit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('employer_match_type', models.CharField(choices=[('group_rrsp', 'Group RRSP'), ('pension_plan', 'Public Service Pension Plan'), ('cpp_qpp', 'Canada Pension Plan / Quebec Pension Plan'), ('other', 'Other')], max_length=20)),
                ('employer_match_max', finance.models.MoneyField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[helpers.validators.number_input_validation])),
                ('employer_match_percent', finance.models.PercentageField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[helpers.validators.number_input_validation])),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Goals',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('target_amount', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('current_amount', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('monthly_contribution', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('start_date', models.DateField(blank=True, null=True)),
                ('target_date', models.DateField(blank=True, null=True)),
                ('category', models.CharField(choices=[('retirement', 'Retirement'), ('education', 'Education'), ('business', 'Business'), ('home', 'Home'), ('car', 'Car'), ('vacation', 'Vacation'), ('wedding', 'Wedding'), ('medical', 'Medical'), ('large_purchase', 'Large Purchase'), ('children_tuition', 'Children Tuition'), ('other', 'Other')], max_length=20)),
                ('is_required', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='InvestmentAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account_type', models.CharField(choices=[('rrsp', 'Registered Retirement Savings Plan'), ('tfsa', 'Tax-Free Savings Account'), ('fhsa', 'First Home Savings Account'), ('non_registered', 'Non-Registered Account'), ('lira', 'Locked-In Retirement Account'), ('resp', 'Registered Education Savings Plan'), ('rdsp', 'Registered Disability Savings Plan'), ('crypto', 'Cryptocurrency Account'), ('other', 'Other')], max_length=20)),
                ('current_amount', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='MonthlyFinance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('month', models.IntegerField()),
                ('year', models.IntegerField()),
                ('income', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('rent_or_mortgage', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('food_grocery', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('utilities', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('transportation', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('insurance', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('healthcare', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('entertainment', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('shopping', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('personal_care', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('saving_tax_amount', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='MonthlyEmployerBenefitPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_contribution', finance.models.MoneyField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[helpers.validators.number_input_validation])),
                ('current_contribution_percent', finance.models.PercentageField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[helpers.validators.number_input_validation])),
                ('employer_benefits', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finance.employerbenefit')),
                ('monthly_finance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finance.monthlyfinance')),
            ],
        ),
        migrations.CreateModel(
            name='MonthlyEmergencyFundPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('contribution_amount', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('contribution_date', models.DateField(blank=True, null=True)),
                ('emergency_fund', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finance.emergencyfund')),
                ('monthly_finance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finance.monthlyfinance')),
            ],
        ),
        migrations.CreateModel(
            name='MonthlyDebtPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('actual_payment', finance.models.MoneyField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[helpers.validators.number_input_validation])),
                ('interest_paid', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('principal_paid', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('payment_date', models.DateField(blank=True, null=True)),
                ('debt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finance.debt')),
                ('monthly_finance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finance.monthlyfinance')),
            ],
        ),
        migrations.CreateModel(
            name='MonthlyGoalPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('contribution_amount', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('contribution_date', models.DateField(blank=True, null=True)),
                ('goal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finance.goals')),
                ('monthly_finance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finance.monthlyfinance')),
            ],
        ),
        migrations.CreateModel(
            name='MonthlyInvestmentAccountPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('contribution_amount', finance.models.MoneyField(decimal_places=2, default=0.0, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('contribution_date', models.DateField(blank=True, null=True)),
                ('investment_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finance.investmentaccount')),
                ('monthly_finance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finance.monthlyfinance')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddIndex(
            model_name='monthlyfinance',
            index=models.Index(fields=['user', 'year', 'month'], name='finance_mon_user_id_3221b6_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='monthlyfinance',
            unique_together={('user', 'month', 'year')},
        ),
        migrations.AlterUniqueTogether(
            name='monthlyemployerbenefitpayment',
            unique_together={('monthly_finance', 'employer_benefits')},
        ),
        migrations.AlterUniqueTogether(
            name='monthlyemergencyfundpayment',
            unique_together={('monthly_finance', 'emergency_fund')},
        ),
        migrations.AlterUniqueTogether(
            name='monthlydebtpayment',
            unique_together={('monthly_finance', 'debt')},
        ),
        migrations.AlterUniqueTogether(
            name='monthlygoalpayment',
            unique_together={('monthly_finance', 'goal')},
        ),
    ]
