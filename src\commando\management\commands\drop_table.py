from django.core.management.base import BaseCommand, CommandError
from django.db import connection

class Command(BaseCommand):
    help = 'Drops the specified table from the database'

    def add_arguments(self, parser):
        parser.add_argument('table_name', type=str, help='The name of the table to drop')
        parser.add_argument('--cascade', action='store_true', help='Drop the table with CASCADE')

    def handle(self, *args, **kwargs):
        table_name = kwargs['table_name']
        cascade = kwargs['cascade']
        
        try:
            with connection.cursor() as cursor:
                if cascade:
                    cursor.execute(f'DROP TABLE IF EXISTS {table_name} CASCADE')
                else:
                    cursor.execute(f'DROP TABLE IF EXISTS {table_name}')
                self.stdout.write(self.style.SUCCESS(f'Successfully dropped the {table_name} table'))
        except Exception as e:
            raise CommandError(f'Error dropping table {table_name}: {e}')
