import calendar
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.conf import settings
from datetime import datetime
from django.http import JsonResponse
from django.core.exceptions import ObjectDoesNotExist
from rest_framework.decorators import api_view
from decimal import Decimal, InvalidOperation
from django.views.decorators.http import require_http_methods
from .models import (MonthlyFinance, BasicFinance, EmergencyFund, EmergencyFundContribution,
                    Debt, MonthlyDebtPayment, EmployerBenefit, MonthlyEmployerBenefitPayment,
                    InvestmentAccount, MonthlyInvestmentAccountPayment,
                    Goals, MonthlyGoalPayment)
from .forms import (MonthlyFinanceForm, BasicFinanceForm, EmergencyFundForm, EmergencyFundContributionForm,
                   DebtForm, MonthlyDebtPaymentForm, EmployerBenefitForm, MonthlyEmployerBenefitPaymentForm,
                   InvestmentAccountForm, MonthlyInvestmentAccountPaymentForm,
                   GoalsForm, MonthlyGoalPaymentForm)

LOGIN_URL = settings.LOGIN_URL

@login_required
def monthly_overview(request, year=None, month=None):
    if request.method == 'GET' and 'year' in request.GET and 'month' in request.GET:
        year = request.GET.get('year')
        month = request.GET.get('month')
        try:
            monthly_finance = MonthlyFinance.objects.get(
                user=request.user,
                year=year,
                month=month
            )
            data = {
                'income': monthly_finance.income,
                'rent_or_mortgage': monthly_finance.rent_or_mortgage,
                'food_grocery': monthly_finance.food_grocery,
                'utilities': monthly_finance.utilities,
                'transportation': monthly_finance.transportation,
                'insurance': monthly_finance.insurance,
                'healthcare': monthly_finance.healthcare,
                'entertainment': monthly_finance.entertainment,
                'shopping': monthly_finance.shopping,
                'personal_care': monthly_finance.personal_care,
                'saving_tax_amount': monthly_finance.saving_tax_amount,
                'object_id': monthly_finance.id
            }
            return JsonResponse(data)
        except MonthlyFinance.DoesNotExist:
            return JsonResponse({'info': 'Data not available for the selected month-year.'})

    if request.method == 'POST':
        data = request.POST.dict()
        object_id = data.pop('object_id', None)

        form = MonthlyFinanceForm(data)
        if form.is_valid():
            cleaned_data = form.cleaned_data
            year = cleaned_data['year']
            month = cleaned_data['month']
            
            defaults = {key: value for key, value in cleaned_data.items() if key not in ['year', 'month']}
            try:
                finance, created = MonthlyFinance.objects.update_or_create(
                    user=request.user,
                    year=year,
                    month=month,
                    defaults=defaults
                )
                return JsonResponse({
                    'status': 'success',
                    'object_id': finance.id,
                    'created': created
                })
            except IntegrityError:
                return JsonResponse({'status': 'error', 'message': 'Duplicate entry exists for this month and year.'}, status=400)
        else:
            return JsonResponse({'status': 'error', 'errors': form.errors})

    # Default GET request handling
    if year is None or month is None:
        latest_finance = MonthlyFinance.objects.filter(user=request.user).order_by('-year', '-month').first()
        if latest_finance:
            year = latest_finance.year
            month = latest_finance.month
        else:
            now = datetime.now()
            year = now.year
            month = now.month

    monthly_finance, created = MonthlyFinance.objects.get_or_create(
        user=request.user,
        year=year,
        month=month,
        defaults={'user': request.user}
    )

    context = {
        'finance_form': MonthlyFinanceForm(instance=monthly_finance),
        'page_title': 'Monthly Overview',
        'active_step': 'monthly',
        'year': year,
        'month': month
    }
    
    return render(request, 'finance/monthly_overview.html', context)

@login_required
def emergency_fund(request):
    # Get or create the emergency fund for the user
    emergency_fund_obj, created = EmergencyFund.objects.get_or_create(
        user=request.user,
        defaults={
            'current_amount': 0,
            'emergency_fund_target': 0
        }
    )
    
    # Calculate progress percentage
    progress_percentage = 0
    if emergency_fund_obj.emergency_fund_target and emergency_fund_obj.emergency_fund_target > 0:
        progress_percentage = min(100, int((emergency_fund_obj.current_amount / emergency_fund_obj.emergency_fund_target) * 100))
    
    # Get all contributions for this emergency fund
    contributions = EmergencyFundContribution.objects.filter(
        emergency_fund=emergency_fund_obj
    ).order_by('-contribution_date')
    
    # Handle AJAX requests
    if request.method == 'POST' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # Handle fund settings update via AJAX
        if 'update_fund' in request.POST:
            fund_form = EmergencyFundForm(request.POST, instance=emergency_fund_obj)
            if fund_form.is_valid():
                fund_form.save()
                return JsonResponse({'status': 'success'})
            return JsonResponse({'status': 'error', 'errors': fund_form.errors})
        
        # Handle new contribution via AJAX
        elif 'add_contribution' in request.POST:
            contribution_form = EmergencyFundContributionForm(request.POST)
            if contribution_form.is_valid():
                contribution = contribution_form.save(commit=False)
                contribution.user = request.user
                contribution.emergency_fund = emergency_fund_obj
                contribution.save()
                
                # Update the current amount in the emergency fund
                emergency_fund_obj.current_amount += contribution.contribution_amount
                emergency_fund_obj.save()
                
                return JsonResponse({'status': 'success'})
            return JsonResponse({'status': 'error', 'errors': contribution_form.errors})
    # Handle regular form submissions (non-AJAX)
    elif request.method == 'POST':
        # Handle fund settings update
        if 'update_fund' in request.POST:
            fund_form = EmergencyFundForm(request.POST, instance=emergency_fund_obj)
            if fund_form.is_valid():
                fund_form.save()
                messages.success(request, 'Emergency fund settings updated successfully.')
                return redirect('finance_emergency_fund')
        # Handle new contribution
        elif 'add_contribution' in request.POST:
            contribution_form = EmergencyFundContributionForm(request.POST)
            if contribution_form.is_valid():
                contribution = contribution_form.save(commit=False)
                contribution.user = request.user
                contribution.emergency_fund = emergency_fund_obj
                contribution.save()
                
                # Update the current amount in the emergency fund
                emergency_fund_obj.current_amount += contribution.contribution_amount
                emergency_fund_obj.save()
                
                messages.success(request, 'Contribution added successfully.')
                return redirect('finance_emergency_fund')
    else:
        # Initialize forms for GET request
        fund_form = EmergencyFundForm(instance=emergency_fund_obj)
        contribution_form = EmergencyFundContributionForm()
    
    context = {
        'emergency_fund': emergency_fund_obj,
        'progress_percentage': progress_percentage,
        'fund_form': fund_form,
        'contribution_form': contribution_form,
        'contributions': contributions,
        'page_title': 'Emergency Fund',
        'active_step': 'emergency_fund'
    }
    
    return render(request, 'finance/emergency_fund.html', context)

@login_required
@require_http_methods(["POST"])
def add_emergency_payment(request):
    fund_id = request.POST.get('emergency_fund')
    emergency_fund_obj = get_object_or_404(EmergencyFund, id=fund_id, user=request.user)
    
    # Get or create the monthly finance record
    year = int(request.POST.get('year'))
    month = int(request.POST.get('month'))
    monthly_finance, created = MonthlyFinance.objects.get_or_create(
        user=request.user, year=year, month=month
    )
    
    form = EmergencyFundContributionForm(request.POST)
    if form.is_valid():
        payment = form.save(commit=False)
        payment.monthly_finance = monthly_finance
        payment.emergency_fund = emergency_fund_obj
        payment.save()
        
        # Update the current amount in the emergency fund
        emergency_fund_obj.current_amount += payment.contribution_amount
        emergency_fund_obj.save()
        
        # For AJAX requests, return JSON
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'status': 'success'})
        
        # For regular form submissions, render with success message
        messages.success(request, 'Emergency fund payment added successfully.')
        
        # Calculate progress percentage
        progress_percentage = 0
        if emergency_fund_obj.emergency_fund_target and emergency_fund_obj.emergency_fund_target > 0:
            progress_percentage = min(100, int((emergency_fund_obj.current_amount / emergency_fund_obj.emergency_fund_target) * 100))
        
        # Get all contributions
        contributions = EmergencyFundContribution.objects.filter(
            emergency_fund=emergency_fund_obj
        ).order_by('-contribution_date')
        
        context = {
            'emergency_fund': emergency_fund_obj,
            'progress_percentage': progress_percentage,
            'fund_form': EmergencyFundForm(instance=emergency_fund_obj),
            'contribution_form': EmergencyFundContributionForm(),
            'contributions': contributions,
            'page_title': 'Emergency Fund',
            'active_step': 'emergency_fund',
            'show_success': True,
            'success_message': 'Emergency fund payment added successfully.'
        }
        
        return render(request, 'finance/emergency_fund.html', context)
    
    # For AJAX requests with errors, return JSON
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'status': 'error', 'errors': form.errors})
    
    # For regular form submissions with errors, render with error message
    messages.error(request, 'Please correct the errors below.')
    
    # Calculate progress percentage
    progress_percentage = 0
    if emergency_fund_obj.emergency_fund_target and emergency_fund_obj.emergency_fund_target > 0:
        progress_percentage = min(100, int((emergency_fund_obj.current_amount / emergency_fund_obj.emergency_fund_target) * 100))
    
    # Get all contributions
    contributions = EmergencyFundContribution.objects.filter(
        emergency_fund=emergency_fund_obj
    ).order_by('-contribution_date')
    
    context = {
        'emergency_fund': emergency_fund_obj,
        'progress_percentage': progress_percentage,
        'fund_form': EmergencyFundForm(instance=emergency_fund_obj),
        'contribution_form': form,  # Use the form with errors
        'contributions': contributions,
        'page_title': 'Emergency Fund',
        'active_step': 'emergency_fund'
    }
    
    return render(request, 'finance/emergency_fund.html', context)

@login_required
@require_http_methods(["POST"])
def delete_emergency_payment(request, payment_id):
    payment = get_object_or_404(EmergencyFundContribution, id=payment_id)
    emergency_fund_obj = payment.emergency_fund
    
    # Only allow deleting if the payment belongs to the user
    if payment.emergency_fund.user != request.user:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)
    
    # Subtract the payment amount from the emergency fund
    emergency_fund_obj.current_amount -= payment.contribution_amount
    emergency_fund_obj.save()
    
    payment.delete()
    return JsonResponse({'status': 'success'})

@login_required
def get_emergency_payments(request, year, month):
    emergency_fund_obj = get_object_or_404(EmergencyFund, user=request.user)
    
    # Filter transactions by the specified month and year
    monthly_finance = MonthlyFinance.objects.filter(
        user=request.user, year=year, month=month
    ).first()
    
    if monthly_finance:
        transactions = EmergencyFundContribution.objects.filter(
            emergency_fund=emergency_fund_obj,
            monthly_finance=monthly_finance
        ).order_by('-contribution_date')
    else:
        transactions = []
    
    # Format transaction data for JSON response
    transaction_data = []
    for t in transactions:
        transaction_data.append({
            'id': t.id,
            'date': t.contribution_date.strftime('%Y-%m-%d') if t.contribution_date else '',
            'amount': float(t.contribution_amount),
            'notes': '',  # Add notes field if you have one
        })
    
    return JsonResponse({'status': 'success', 'transactions': transaction_data})

@login_required
def get_emergency_contribution(request, contribution_id):
    """Get details of a specific emergency fund contribution"""
    contribution = get_object_or_404(EmergencyFundContribution, id=contribution_id)
    
    # Check if the contribution belongs to the user
    if contribution.emergency_fund.user != request.user:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)
    
    # Return the contribution details
    return JsonResponse({
        'status': 'success',
        'contribution': {
            'id': contribution.id,
            'contribution_amount': contribution.contribution_amount,
            'contribution_date': contribution.contribution_date.strftime('%Y-%m-%d'),
            'notes': contribution.notes
        }
    })

@login_required
@require_http_methods(["POST"])
def edit_emergency_contribution(request, contribution_id):
    """Edit an emergency fund contribution"""
    contribution = get_object_or_404(EmergencyFundContribution, id=contribution_id)
    emergency_fund_obj = contribution.emergency_fund
    
    # Check if the contribution belongs to the user
    if emergency_fund_obj.user != request.user:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)
    
    # Get the old amount to adjust the emergency fund total
    old_amount = contribution.contribution_amount
    
    # Get form data
    try:
        # Validate the input data
        errors = {}
        
        # Validate amount
        try:
            new_amount_str = request.POST.get('contribution_amount', '0')
            new_amount = Decimal(new_amount_str)  # Convert to Decimal instead of float
            if new_amount <= 0:
                errors['contribution_amount'] = ['Amount must be greater than zero']
        except (ValueError, InvalidOperation):
            errors['contribution_amount'] = ['Invalid amount format']
        
        # Validate date
        contribution_date = request.POST.get('contribution_date', '')
        if not contribution_date:
            errors['contribution_date'] = ['Date is required']
        
        # If there are validation errors, return them
        if errors:
            return JsonResponse({'status': 'error', 'errors': errors})
        
        # Get notes (optional)
        notes = request.POST.get('notes', '')
        
        # Update the emergency fund total
        emergency_fund_obj.current_amount = emergency_fund_obj.current_amount - old_amount + new_amount
        emergency_fund_obj.save()
        
        # Update the contribution
        contribution.contribution_amount = new_amount
        contribution.contribution_date = contribution_date
        contribution.notes = notes
        contribution.save()
        
        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
@require_http_methods(["POST"])
def delete_emergency_contribution(request, contribution_id):
    """Delete an emergency fund contribution"""
    contribution = get_object_or_404(EmergencyFundContribution, id=contribution_id)
    emergency_fund_obj = contribution.emergency_fund
    
    # Check if the contribution belongs to the user
    if emergency_fund_obj.user != request.user:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)
    
    try:
        # Subtract the contribution amount from the emergency fund total
        emergency_fund_obj.current_amount -= contribution.contribution_amount
        emergency_fund_obj.save()
        
        # Delete the contribution
        contribution.delete()
        
        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def debt_management(request, year=None, month=None):
    if year is None or month is None:
        now = datetime.now()
        year = now.year
        month = now.month

    monthly_finance, created = MonthlyFinance.objects.get_or_create(
        user=request.user, year=year, month=month
    )

    debts = Debt.objects.filter(user=request.user).order_by('name')
    existing_payments = MonthlyDebtPayment.objects.filter(
        monthly_finance=monthly_finance
    ).select_related('debt')
    
    context = {
        'debts': debts,
        'existing_payments': existing_payments,
        'debt_form': DebtForm(),
        'payment_form': MonthlyDebtPaymentForm(),
        'page_title': 'Debt Management',
        'active_step': 'debt',
        'year': year,
        'month': month,
        'monthly_finance': monthly_finance
    }
    return render(request, 'finance/debt_management.html', context)

@login_required
def handle_create_debt(request):
    if request.method == 'POST':
        form = DebtForm(request.POST)
        if form.is_valid():
            debt = form.save(commit=False)
            debt.user = request.user
            debt.save()
            return JsonResponse({
                'status': 'success',
                'message': f'Debt "{debt.name}" created successfully',
                'debt_id': debt.id
            })
        return JsonResponse({'status': 'error', 'errors': form.errors})
    return JsonResponse({'status': 'error', 'message': 'Invalid request method'})

@login_required
def handle_update_debt(request, debt_id):
    if request.method == 'POST':
        debt = get_object_or_404(Debt, id=debt_id, user=request.user)
        form = DebtForm(request.POST, instance=debt)
        if form.is_valid():
            updated_debt = form.save()
            return JsonResponse({
                'status': 'success',
                'message': f'Debt "{updated_debt.name}" updated successfully'
            })
        return JsonResponse({'status': 'error', 'errors': form.errors})
    return JsonResponse({'status': 'error', 'message': 'Invalid request method'})

@login_required
def handle_delete_debt(request, debt_id):
    if request.method == 'POST':
        debt = get_object_or_404(Debt, id=debt_id, user=request.user)
        debt_name = debt.name
        debt.delete()
        return JsonResponse({
            'status': 'success',
            'message': f'Debt "{debt_name}" deleted successfully'
        })
    return JsonResponse({'status': 'error', 'message': 'Invalid request method'})

@login_required
@require_http_methods(["POST"])
def handle_add_payment(request):
    debt_id = request.POST.get('debt_id')
    year = request.POST.get('year')
    month = request.POST.get('month')
    
    if not all([debt_id, year, month]):
        return JsonResponse({
            'status': 'error', 
            'message': 'Missing required fields: debt, year, or month'
        })
    
    try:
        debt = get_object_or_404(Debt, id=debt_id, user=request.user)
        monthly_finance, created = MonthlyFinance.objects.get_or_create(
            user=request.user, 
            year=int(year), 
            month=int(month)
        )
        
        # Check if payment already exists
        existing_payment = MonthlyDebtPayment.objects.filter(
            monthly_finance=monthly_finance,
            debt=debt
        ).first()
        
        if existing_payment:
            return JsonResponse({
                'status': 'error', 
                'message': f'Payment for {debt.name} already exists for {calendar.month_name[int(month)]} {year}. Use edit instead.'
            })
        
        # Create the payment
        payment = MonthlyDebtPayment(
            monthly_finance=monthly_finance,
            debt=debt,
            interest_paid=Decimal(request.POST.get('interest_paid', 0)),
            principal_paid=Decimal(request.POST.get('principal_paid', 0)),
            payment_date=request.POST.get('payment_date'),
            notes=request.POST.get('notes', '')
        )
        
        # Calculate actual payment
        payment.actual_payment = payment.interest_paid + payment.principal_paid
        payment.save()
        
        return JsonResponse({
            'status': 'success',
            'message': f'Payment added for {debt.name}',
            'payment_id': payment.id
        })
        
    except ValueError as e:
        return JsonResponse({
            'status': 'error',
            'message': 'Invalid number format in payment amounts'
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Error adding payment: {str(e)}'
        })

@login_required
def get_payment_detail(request, payment_id):
    payment = get_object_or_404(MonthlyDebtPayment, id=payment_id, debt__user=request.user)
    return JsonResponse({
        'id': payment.id,
        'debt_id': payment.debt.id,
        'debt_name': payment.debt.name,
        'actual_payment': float(payment.actual_payment or 0),
        'interest_paid': float(payment.interest_paid),
        'principal_paid': float(payment.principal_paid),
        'payment_date': payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else '',
        'notes': payment.notes or ''
    })

@login_required
def handle_update_payment(request, payment_id):
    if request.method == 'POST':
        payment = get_object_or_404(MonthlyDebtPayment, id=payment_id, debt__user=request.user)
        form = MonthlyDebtPaymentForm(request.POST, instance=payment)
        if form.is_valid():
            updated_payment = form.save()
            return JsonResponse({
                'status': 'success',
                'message': f'Payment updated for {payment.debt.name}'
            })
        return JsonResponse({'status': 'error', 'errors': form.errors})
    return JsonResponse({'status': 'error', 'message': 'Invalid request method'})

@login_required
def handle_delete_payment(request, payment_id):
    if request.method == 'POST':
        payment = get_object_or_404(MonthlyDebtPayment, id=payment_id, debt__user=request.user)
        debt_name = payment.debt.name
        payment.delete()
        return JsonResponse({
            'status': 'success',
            'message': f'Payment deleted for {debt_name}'
        })
    return JsonResponse({'status': 'error', 'message': 'Invalid request method'})

@login_required
def get_debt_summary(request):
    """Get debt summary statistics"""
    debts = Debt.objects.filter(user=request.user, is_paid_off=False)
    
    total_debt = sum(debt.remaining_balance or 0 for debt in debts)
    monthly_payments = sum(debt.minimum_payment or 0 for debt in debts)
    
    # Calculate DTI ratio if user has income data
    try:
        latest_finance = MonthlyFinance.objects.filter(user=request.user).order_by('-year', '-month').first()
        if latest_finance and latest_finance.net_income:
            dti_ratio = f"{(monthly_payments / latest_finance.net_income * 100):.1f}%"
        else:
            dti_ratio = "N/A"
    except:
        dti_ratio = "N/A"
    
    return JsonResponse({
        'total_debt': float(total_debt),
        'monthly_payments': float(monthly_payments),
        'dti_ratio': dti_ratio
    })

@login_required
def get_debt_list(request):
    """Get list of user's debts"""
    debts = Debt.objects.filter(user=request.user, is_paid_off=False).order_by('name')
    
    debt_data = []
    for debt in debts:
        debt_data.append({
            'id': debt.id,
            'name': debt.name,
            'remaining_balance': float(debt.remaining_balance or 0),
            'minimum_payment': float(debt.minimum_payment or 0)
        })
    
    return JsonResponse({
        'status': 'success',
        'debts': debt_data
    })

@login_required
def get_debt_detail(request, debt_id):
    """Get debt details for editing"""
    debt = get_object_or_404(Debt, id=debt_id, user=request.user)
    
    return JsonResponse({
        'id': debt.id,
        'name': debt.name,
        'initial_amount': float(debt.initial_amount),
        'interest_rate': float(debt.interest_rate),
        'minimum_payment': float(debt.minimum_payment),
        'planned_payment': float(debt.planned_payment),
        'remaining_balance': float(debt.remaining_balance),
        'start_date': debt.start_date.strftime('%Y-%m-%d'),
        'estimated_end_date': debt.estimated_end_date.strftime('%Y-%m-%d') if debt.estimated_end_date else '',
        'is_paid_off': debt.is_paid_off
    })

@login_required
def delete_debt(request, debt_id):
    """Delete a debt"""
    if request.method == 'POST':
        debt = get_object_or_404(Debt, id=debt_id, user=request.user)
        debt.delete()
        return JsonResponse({'status': 'success'})
    return JsonResponse({'status': 'error'})

@login_required
def save_debt_payment(request):
    """Save debt payment"""
    if request.method == 'POST':
        debt_id = request.POST.get('debt_id')
        debt = get_object_or_404(Debt, id=debt_id, user=request.user)
        
        # Get or create monthly finance record
        now = datetime.now()
        monthly_finance, _ = MonthlyFinance.objects.get_or_create(
            user=request.user, 
            year=now.year, 
            month=now.month
        )
        
        # Get or create payment record
        payment, created = MonthlyDebtPayment.objects.get_or_create(
            monthly_finance=monthly_finance,
            debt=debt,
            defaults={
                'interest_paid': 0.00,
                'principal_paid': 0.00,
            }
        )
        
        form = MonthlyDebtPaymentForm(request.POST, instance=payment)
        if form.is_valid():
            saved_payment = form.save()
            return JsonResponse({
                'status': 'success',
                'payment_id': saved_payment.id,
                'created': created
            })
        return JsonResponse({'status': 'error', 'errors': form.errors})
    
    return JsonResponse({'status': 'error'})

@login_required
def get_debt_payments(request, year, month):
    """Get debt payments for a specific month"""
    try:
        monthly_finance = MonthlyFinance.objects.get(
            user=request.user, 
            year=year, 
            month=month
        )
        payments = MonthlyDebtPayment.objects.filter(
            monthly_finance=monthly_finance
        ).select_related('debt').order_by('debt__name')
        
        payment_data = []
        for payment in payments:
            payment_data.append({
                'id': payment.id,
                'debt_name': payment.debt.name,
                'actual_payment': float(payment.actual_payment) if payment.actual_payment else 0,
                'interest_paid': float(payment.interest_paid),
                'principal_paid': float(payment.principal_paid),
                'payment_date': payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else '',
                'notes': payment.notes or ''
            })
        
        return JsonResponse({
            'status': 'success',
            'payments': payment_data,
            'month_year': f"{calendar.month_name[int(month)]} {year}"
        })
    except MonthlyFinance.DoesNotExist:
        return JsonResponse({
            'status': 'success',
            'payments': [],
            'month_year': f"{calendar.month_name[int(month)]} {year}"
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        })

@login_required
def employer_benefits(request, year=None, month=None):
    if year is None or month is None:
        latest_finance = MonthlyFinance.objects.filter(user=request.user).order_by('-year', '-month').first()
        if latest_finance:
            year = latest_finance.year
            month = latest_finance.month
        else:
            now = datetime.now()
            year = now.year
            month = now.month

    monthly_finance, created = MonthlyFinance.objects.get_or_create(
        user=request.user, year=year, month=month
    )

    if request.method == 'POST':
        if 'create_benefit' in request.POST:
            form = EmployerBenefitForm(request.POST)
            if form.is_valid():
                benefit = form.save(commit=False)
                benefit.user = request.user
                benefit.save()
                return JsonResponse({'status': 'success', 'id': benefit.id})
            return JsonResponse({'status': 'error', 'errors': form.errors})
        
        elif 'update_benefit' in request.POST:
            benefit_id = request.POST.get('benefit_id')
            benefit = get_object_or_404(EmployerBenefit, id=benefit_id, user=request.user)
            form = EmployerBenefitForm(request.POST, instance=benefit)
            if form.is_valid():
                form.save()
                return JsonResponse({'status': 'success'})
            return JsonResponse({'status': 'error', 'errors': form.errors})
        
        elif 'update_payment' in request.POST:
            payment_id = request.POST.get('payment_id')
            payment = get_object_or_404(MonthlyEmployerBenefitPayment, id=payment_id)
            form = MonthlyEmployerBenefitPaymentForm(request.POST, instance=payment)
            if form.is_valid():
                form.save()
                return JsonResponse({'status': 'success'})
            return JsonResponse({'status': 'error', 'errors': form.errors})

    employer_benefits = EmployerBenefit.objects.filter(user=request.user, is_active=True)
    benefit_payments = MonthlyEmployerBenefitPayment.objects.filter(
        monthly_finance=monthly_finance
    ).select_related('employer_benefits')

    context = {
        'employer_benefits': employer_benefits,
        'benefit_payments': benefit_payments,
        'benefit_form': EmployerBenefitForm(),
        'payment_form': MonthlyEmployerBenefitPaymentForm(),
        'page_title': 'Employer Benefits',
        'active_step': 'benefits',
        'year': year,
        'month': month
    }
    
    return render(request, 'finance/employer_benefits.html', context)

@login_required
def investment_accounts(request, year=None, month=None):
    if year is None or month is None:
        latest_finance = MonthlyFinance.objects.filter(user=request.user).order_by('-year', '-month').first()
        if latest_finance:
            year = latest_finance.year
            month = latest_finance.month
        else:
            now = datetime.now()
            year = now.year
            month = now.month

    monthly_finance, created = MonthlyFinance.objects.get_or_create(
        user=request.user, year=year, month=month
    )

    if request.method == 'POST':
        if 'create_account' in request.POST:
            form = InvestmentAccountForm(request.POST)
            if form.is_valid():
                account = form.save(commit=False)
                account.user = request.user
                account.save()
                return JsonResponse({'status': 'success', 'id': account.id})
            return JsonResponse({'status': 'error', 'errors': form.errors})
        
        elif 'update_account' in request.POST:
            account_id = request.POST.get('account_id')
            account = get_object_or_404(InvestmentAccount, id=account_id, user=request.user)
            form = InvestmentAccountForm(request.POST, instance=account)
            if form.is_valid():
                form.save()
                return JsonResponse({'status': 'success'})
            return JsonResponse({'status': 'error', 'errors': form.errors})
        
        elif 'update_payment' in request.POST:
            payment_id = request.POST.get('payment_id')
            payment = get_object_or_404(MonthlyInvestmentAccountPayment, id=payment_id)
            form = MonthlyInvestmentAccountPaymentForm(request.POST, instance=payment)
            if form.is_valid():
                form.save()
                return JsonResponse({'status': 'success'})
            return JsonResponse({'status': 'error', 'errors': form.errors})

    investment_accounts = InvestmentAccount.objects.filter(user=request.user, is_active=True)
    account_payments = MonthlyInvestmentAccountPayment.objects.filter(
        monthly_finance=monthly_finance
    ).select_related('investment_account')

    context = {
        'investment_accounts': investment_accounts,
        'account_payments': account_payments,
        'account_form': InvestmentAccountForm(),
        'payment_form': MonthlyInvestmentAccountPaymentForm(),
        'page_title': 'Investment Accounts',
        'active_step': 'investments',
        'year': year,
        'month': month
    }
    
    return render(request, 'finance/investment_accounts.html', context)

@login_required
def financial_goals(request, year=None, month=None):
    if year is None or month is None:
        latest_finance = MonthlyFinance.objects.filter(user=request.user).order_by('-year', '-month').first()
        if latest_finance:
            year = latest_finance.year
            month = latest_finance.month
        else:
            now = datetime.now()
            year = now.year
            month = now.month

    monthly_finance, created = MonthlyFinance.objects.get_or_create(
        user=request.user, year=year, month=month
    )

    if request.method == 'POST':
        if 'create_goal' in request.POST:
            form = GoalsForm(request.POST)
            if form.is_valid():
                goal = form.save(commit=False)
                goal.user = request.user
                goal.save()
                return JsonResponse({'status': 'success', 'id': goal.id})
            return JsonResponse({'status': 'error', 'errors': form.errors})
        
        elif 'update_goal' in request.POST:
            goal_id = request.POST.get('goal_id')
            goal = get_object_or_404(Goals, id=goal_id, user=request.user)
            form = GoalsForm(request.POST, instance=goal)
            if form.is_valid():
                form.save()
                return JsonResponse({'status': 'success'})
            return JsonResponse({'status': 'error', 'errors': form.errors})
        
        elif 'update_payment' in request.POST:
            payment_id = request.POST.get('payment_id')
            payment = get_object_or_404(MonthlyGoalPayment, id=payment_id)
            form = MonthlyGoalPaymentForm(request.POST, instance=payment)
            if form.is_valid():
                form.save()
                return JsonResponse({'status': 'success'})
            return JsonResponse({'status': 'error', 'errors': form.errors})

    goals = Goals.objects.filter(user=request.user, is_active=True)
    goal_payments = MonthlyGoalPayment.objects.filter(
        monthly_finance=monthly_finance
    ).select_related('goal')

    context = {
        'goals': goals,
        'goal_payments': goal_payments,
        'goal_form': GoalsForm(),
        'payment_form': MonthlyGoalPaymentForm(),
        'page_title': 'Financial Goals',
        'active_step': 'goals',
        'year': year,
        'month': month
    }
    
    return render(request, 'finance/financial_goals.html', context)
