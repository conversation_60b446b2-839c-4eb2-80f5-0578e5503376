import plotly.graph_objs as go
from plotly.offline import plot
from django.shortcuts import render
from django.conf import settings
from django.contrib.auth.decorators import login_required
from datetime import datetime

from finance.models import MonthlyFinance, BasicFinance
from .models import CanadianAverageExpense


LOGIN_URL = settings.LOGIN_URL

@login_required(login_url=LOGIN_URL)
def dashboard_view(request):

     # Initialize variables before the try block
    latest_finance = None

    try:
        # Get latest monthly finance data
        latest_finance = MonthlyFinance.objects.filter(
            user=request.user
        ).order_by('-year', '-month').first()

    except MonthlyFinance.DoesNotExist:
        latest_finance = None

    try:
        canadian_avg = CanadianAverageExpense.objects.all()
    except CanadianAverageExpense.DoesNotExist:
        canadian_avg = None

    # Create the comparison chart
    plot_div = None
    context_data = None

    if latest_finance and canadian_avg:
        # Calculate user's percentages
        try:
            user_rent_or_mortgage_percentage = (latest_finance.rent_or_mortgage / latest_finance.income * 100) if latest_finance.income else 0
            user_food_grocery_percentage = (latest_finance.food_grocery / latest_finance.income * 100) if latest_finance.income else 0
        except (AttributeError, ZeroDivisionError):
            user_rent_or_mortgage_percentage = 0
            user_food_grocery_percentage = 0

        categories = ['Rent/Mortgage', 'Food/Grocery']
        user_data = [user_rent_or_mortgage_percentage, user_food_grocery_percentage]
        
        # Create traces for each province
        province_traces = []
        for province in canadian_avg:
            trace = go.Bar(
                name=province.province,
                x=categories,
                y=[province.rent_percentage, province.food_percentage],
                text=[f'{province.rent_percentage:.2f}%', f'{province.food_percentage:.2f}%'],
                textposition='auto'
            )
            province_traces.append(trace)

        # Add user's data
        user_trace = go.Bar(
            name='Your Percentage',
            x=categories,
            y=user_data,
            text=[f'{y:.2f}%' for y in user_data],
            textposition='auto',
            marker_color='rgba(55, 128, 191, 0.7)',
            marker_line_color='rgba(55, 128, 191, 1.0)',
            marker_line_width=2,
        )

        # Combine all traces
        all_traces = province_traces + [user_trace]

        layout = go.Layout(
            title='Your Expenses vs. Canadian Provincial Averages (% of Income)',
            barmode='group',
            yaxis_title='Percentage of Income',
        )

        fig = go.Figure(data=all_traces, layout=layout)
        plot_div = plot(fig, output_type='div', include_plotlyjs=True)

        # Get additional financial information
        emergency_funds = latest_finance.monthlyemergencyfundpayment_set.all()
        debt_payments = latest_finance.monthlydebtpayment_set.all()
        benefit_payments = latest_finance.monthlyemployerbenefitpayment_set.all()
        investment_payments = latest_finance.monthlyinvestmentaccountpayment_set.all()
        goal_payments = latest_finance.monthlygoalpayment_set.all()

        # Prepare context data
        context_data = {
            'user': {
                'rent': user_rent_or_mortgage_percentage,
                'food': user_food_grocery_percentage,
            },
            'provinces': [
                {
                    'name': province.province,
                    'rent': province.rent_percentage,
                    'food': province.food_percentage,
                    'rent_diff': user_rent_or_mortgage_percentage - province.rent_percentage,
                    'food_diff': user_food_grocery_percentage - province.food_percentage,
                }
                for province in canadian_avg
            ],
            'emergency_funds': emergency_funds,
            'debt_payments': debt_payments,
            'benefit_payments': benefit_payments,
            'investment_payments': investment_payments,
            'goal_payments': goal_payments
        }

    page_title = "Dashboard"
    my_context = {
        "page_title": page_title,
        "room_name": "broadcast",
        "latest_finance": latest_finance,
        "plot_div": plot_div,
        "context_data": context_data,
    }
    
    return render(request, "dashboard/main.html", my_context)
