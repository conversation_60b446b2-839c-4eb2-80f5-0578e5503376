from django.core.exceptions import ValidationError
import re

def number_input_validation(value):

    # Convert the value to a string to ensure compatibility with regex operations
    value_str = str(value)
    
    # Regular expression to match only positive numbers with optional decimal places
    # This regex allows digits and a single optional decimal point
    if not re.fullmatch(r'\d+(\.\d+)?', value_str):
        raise ValidationError('Only positive numbers are allowed.')
    
    # Additionally, ensure there are no mathematical signs or other invalid characters
    # Check if the value contains any invalid characters or sequences
    if re.search(r'[^0-9.]', value_str):
        raise ValidationError('Invalid input. Only positive numbers are allowed.')
    
    # Ensure the value does not contain multiple decimal points
    if value_str.count('.') > 1:
        raise ValidationError('Invalid input. Only positive numbers are allowed.')
    
    # Check for negative numbers or numbers with mathematical signs
    try:
        if float(value_str) < 0:
            raise ValidationError('Only positive numbers are allowed.')
    except ValueError:
        raise ValidationError('Invalid input. Only positive numbers are allowed.')