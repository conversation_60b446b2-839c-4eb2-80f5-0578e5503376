# Generated by Django 5.0.2 on 2025-05-19 06:50

import django.db.models.deletion
import finance.models
import helpers.validators
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('finance', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmergencyFundContribution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('contribution_amount', finance.models.MoneyField(decimal_places=2, max_digits=10, validators=[helpers.validators.number_input_validation])),
                ('contribution_date', models.DateField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('emergency_fund', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contributions', to='finance.emergencyfund')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-contribution_date'],
            },
        ),
        migrations.DeleteModel(
            name='MonthlyEmergencyFundPayment',
        ),
    ]
