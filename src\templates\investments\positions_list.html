{% extends 'investments/base_investments.html' %}
{% load static %}

{% block investments_content %}
<div class="finance-form-container">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-2xl font-bold mb-6 text-primary">Current Positions</h1>
        
        <div class="intro-text mb-6">
            <p class="text-sm opacity-75">
                View all your current investment positions across all brokerages.
            </p>
        </div>

        <!-- Positions Table -->
        <div class="form-section">
            <div class="flex justify-between items-center mb-4">
                <h2 class="section-title">Holdings</h2>
                <div class="flex space-x-3">
                    <button type="button" class="btn-secondary" onclick="refreshPrices()">
                        <svg class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Refresh Prices
                    </button>
                </div>
            </div>

            {% if positions %}
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Asset</th>
                            <th>Quantity</th>
                            <th>Avg Cost</th>
                            <th>Current Price</th>
                            <th>Market Value</th>
                            <th>Cost Basis</th>
                            <th>Unrealized P&L</th>
                            <th>% Change</th>
                            <th>Brokerage</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for position in positions %}
                        <tr>
                            <td>
                                <div class="flex items-center">
                                    <span class="font-medium">{{ position.asset.ticker }}</span>
                                    <span class="text-sm text-gray-500 ml-2">{{ position.asset.name|truncatechars:30 }}</span>
                                </div>
                            </td>
                            <td class="text-right">{{ position.quantity|floatformat:4 }}</td>
                            <td class="text-right">${{ position.average_cost|floatformat:2 }}</td>
                            <td class="text-right">
                                {% if position.current_price %}
                                    ${{ position.current_price|floatformat:2 }}
                                {% else %}
                                    <span class="text-gray-400">N/A</span>
                                {% endif %}
                            </td>
                            <td class="text-right">
                                {% if position.market_value %}
                                    ${{ position.market_value|floatformat:2 }}
                                {% else %}
                                    <span class="text-gray-400">N/A</span>
                                {% endif %}
                            </td>
                            <td class="text-right">${{ position.total_cost_basis|floatformat:2 }}</td>
                            <td class="text-right">
                                <span class="{% if position.unrealized_gain_loss >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                    ${{ position.unrealized_gain_loss|floatformat:2 }}
                                </span>
                            </td>
                            <td class="text-right">
                                <span class="{% if position.unrealized_gain_loss_percent >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                    {{ position.unrealized_gain_loss_percent|floatformat:2 }}%
                                </span>
                            </td>
                            <td>{{ position.brokerage.name }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-12">
                <svg class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Positions Found</h3>
                <p class="text-gray-600 mb-6">You don't have any current positions. Start by adding some transactions.</p>
                <a href="{% url 'transaction_create' %}" class="btn-save">Add Transaction</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% endblock investments_content %}

{% block investments_js %}
<script>
function refreshPrices() {
    // Placeholder for price refresh functionality
    alert('Price refresh functionality will be implemented in the next phase.');
}
</script>
{% endblock investments_js %}
