from django.urls import path
from . import views

urlpatterns = [
    # Transaction URLs
    path('transactions/', views.TransactionListView.as_view(), name='transaction_list'),
    path('transactions/new/', views.TransactionCreateView.as_view(), name='transaction_create'),
    path('transactions/<int:pk>/edit/', views.TransactionUpdateView.as_view(), name='transaction_update'),
    path('transactions/<int:pk>/delete/', views.TransactionDeleteView.as_view(), name='transaction_delete'),

    # Portfolio URLs
    path('portfolio/', views.PortfolioOverviewView.as_view(), name='portfolio_overview'),
    path('positions/', views.PositionsListView.as_view(), name='positions_list'),

    # Income & Dividends
    path('dividends/', views.DividendsListView.as_view(), name='dividends_list'),

    # Analytics & Reports
    path('analytics/', views.PerformanceAnalyticsView.as_view(), name='performance_analytics'),
    path('tax-lots/', views.TaxLotsListView.as_view(), name='tax_lots_list'),

    # Alerts
    path('alerts/', views.AlertsListView.as_view(), name='alerts_list'),

    # Asset Management
    path('assets/new/', views.AssetCreateView.as_view(), name='asset_create'),
]