# Generated by Django 5.0.2 on 2024-07-31 02:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ActivityLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ip_address", models.GenericIPAddressField()),
                ("user_agent", models.TextField()),
                ("page", models.<PERSON>r<PERSON>ield(max_length=200)),
                ("time_of_visit", models.DateTimeField(auto_now_add=True)),
                ("location", models.CharField(blank=True, max_length=200, null=True)),
                ("session_key", models.CharField(blank=True, max_length=40, null=True)),
                (
                    "user",
                    models.Foreign<PERSON>ey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
