{% extends 'finance/base_finance.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block finance_content %}
<div class="container mx-auto px-4 py-8">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-2">Total Goals</h3>
            <p class="text-2xl font-bold text-blue-600"><span id="total-goals">0</span></p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-2">Goals Completed</h3>
            <p class="text-2xl font-bold text-green-600"><span id="completed-goals">0</span></p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-2">Total Saved</h3>
            <p class="text-2xl font-bold text-purple-600">$<span id="total-saved">0.00</span></p>
        </div>
    </div>

    <!-- Financial Goals Section -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">Financial Goals</h2>
                <button id="add-goal-btn" class="btn-primary">
                    Add Financial Goal
                </button>
            </div>

            <!-- Goals List -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-2 text-left">Goal Name</th>
                            <th class="px-4 py-2 text-left">Target Date</th>
                            <th class="px-4 py-2 text-right">Target Amount</th>
                            <th class="px-4 py-2 text-right">Current Amount</th>
                            <th class="px-4 py-2 text-center">Progress</th>
                            <th class="px-4 py-2 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for goal in goals %}
                        <tr class="border-t">
                            <td class="px-4 py-2">{{ goal.name }}</td>
                            <td class="px-4 py-2">{{ goal.target_date|date:"M Y" }}</td>
                            <td class="px-4 py-2 text-right">${{ goal.target_amount|floatformat:2 }}</td>
                            <td class="px-4 py-2 text-right">${{ goal.current_amount|floatformat:2 }}</td>
                            <td class="px-4 py-2">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ goal.progress }}%"></div>
                                </div>
                                <span class="text-sm text-gray-600 text-center block mt-1">{{ goal.progress }}%</span>
                            </td>
                            <td class="px-4 py-2 text-center">
                                <button class="edit-goal btn-secondary" data-id="{{ goal.id }}">Edit</button>
                                <button class="delete-goal btn-danger" data-id="{{ goal.id }}">Delete</button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-4 py-4 text-center text-gray-500">
                                No financial goals found. Click "Add Financial Goal" to create one.
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Monthly Contributions Section -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="p-6">
            <h2 class="text-xl font-bold mb-6">Monthly Contributions</h2>
            
            <!-- Contribution Filter -->
            <div class="flex gap-4 mb-6">
                <input type="month" id="contribution-month-filter" class="form-input">
                <button id="search-contributions" class="btn-secondary">Search</button>
            </div>

            <!-- Contributions Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-2 text-left">Goal</th>
                            <th class="px-4 py-2 text-left">Date</th>
                            <th class="px-4 py-2 text-right">Amount</th>
                            <th class="px-4 py-2 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="contributions-table">
                        {% for payment in goal_payments %}
                        <tr class="border-t">
                            <td class="px-4 py-2">{{ payment.goal.name }}</td>
                            <td class="px-4 py-2">{{ payment.payment_date|date:"M Y" }}</td>
                            <td class="px-4 py-2 text-right">${{ payment.amount|floatformat:2 }}</td>
                            <td class="px-4 py-2 text-center">
                                <button class="edit-payment btn-secondary" data-id="{{ payment.id }}">Edit</button>
                                <button class="delete-payment btn-danger" data-id="{{ payment.id }}">Delete</button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="px-4 py-4 text-center text-gray-500">
                                No contributions found for the selected period.
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex justify-between mt-8">
        <button id="prev-step" class="btn-secondary">← Investment Accounts</button>
        <button id="complete-plan" class="btn-primary">Complete Financial Plan</button>
    </div>
</div>

<!-- Goal Modal -->
<div id="goal-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Financial Goal
                </h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="goal-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form id="goal-form" class="p-4 md:p-5">
                {% csrf_token %}
                {{ goal_form|crispy }}
                <div class="flex justify-end gap-4 mt-4">
                    <button type="button" class="btn-secondary" data-modal-hide="goal-modal">Cancel</button>
                    <button type="submit" class="btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block finance_js %}
<script src="{% static 'finance/financial_goals.js' %}"></script>
{% endblock %}


