{% extends 'investments/base_investments.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block investments_content %}
<div class="finance-form-container">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6 text-primary">
            {% if object %}Edit Transaction{% else %}Add New Transaction{% endif %}
        </h1>

        <div class="intro-text mb-6">
            <p class="text-sm opacity-75">
                {% if object %}
                    Update the transaction details and ownership information.
                {% else %}
                    Record a new investment transaction with detailed information and ownership splits.
                {% endif %}
            </p>
        </div>

        <form method="post" class="space-y-8" id="transaction-form">
            {% csrf_token %}

            <!-- Error Display -->
            {% if form.errors %}
                <div class="error-container">
                    <div class="error-header">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                        Please correct the following errors:
                    </div>
                    <ul class="error-list">
                        {% for field, errors in form.errors.items %}
                            {% for error in errors %}
                                <li>{{ field|title }}: {{ error }}</li>
                            {% endfor %}
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}

            <!-- Basic Transaction Information -->
            <div class="form-section">
                <h2 class="section-title">Transaction Details</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-field">
                        <label for="{{ form.brokerage.id_for_label }}" class="form-label">Brokerage *</label>
                        {{ form.brokerage }}
                        {% if form.brokerage.help_text %}
                            <p class="form-help">{{ form.brokerage.help_text }}</p>
                        {% endif %}
                    </div>

                    <div class="form-field">
                        <label for="{{ form.asset.id_for_label }}" class="form-label">Asset *</label>
                        <div class="flex space-x-2">
                            {{ form.asset }}
                            <button type="button" class="btn-secondary" onclick="openAssetModal()">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            </button>
                        </div>
                        {% if form.asset.help_text %}
                            <p class="form-help">{{ form.asset.help_text }}</p>
                        {% endif %}
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="form-field">
                        <label for="{{ form.date.id_for_label }}" class="form-label">Date *</label>
                        {{ form.date }}
                    </div>

                    <div class="form-field">
                        <label for="{{ form.action.id_for_label }}" class="form-label">Action *</label>
                        {{ form.action }}
                    </div>

                    <div class="form-field">
                        <label for="{{ form.currency.id_for_label }}" class="form-label">Currency *</label>
                        {{ form.currency }}
                    </div>
                </div>
            </div>

            <!-- Pricing Information -->
            <div class="form-section">
                <h2 class="section-title">Pricing & Quantity</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-field">
                        <label for="{{ form.quantity.id_for_label }}" class="form-label">Quantity *</label>
                        {{ form.quantity }}
                        <p class="form-help">Number of shares/units (use positive numbers, sign will be auto-adjusted)</p>
                    </div>

                    <div class="form-field">
                        <label for="{{ form.price.id_for_label }}" class="form-label">Price per Share *</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            {{ form.price }}
                        </div>
                        <p class="form-help">Price per share/unit in the selected currency</p>
                    </div>
                </div>

                <!-- Calculated Total -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="font-medium">Gross Amount:</span>
                        <span id="gross-amount" class="text-lg font-bold">$0.00</span>
                    </div>
                </div>
            </div>

            <!-- Fees & Costs -->
            <div class="form-section">
                <h2 class="section-title">Fees & Costs</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="form-field">
                        <label for="{{ form.fees.id_for_label }}" class="form-label">General Fees</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            {{ form.fees }}
                        </div>
                        <p class="form-help">General transaction fees</p>
                    </div>

                    <div class="form-field">
                        <label for="{{ form.commission.id_for_label }}" class="form-label">Commission</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            {{ form.commission }}
                        </div>
                        <p class="form-help">Broker commission</p>
                    </div>

                    <div class="form-field">
                        <label for="{{ form.sec_fee.id_for_label }}" class="form-label">SEC Fee</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            {{ form.sec_fee }}
                        </div>
                        <p class="form-help">SEC regulatory fees</p>
                    </div>

                    <div class="form-field">
                        <label for="{{ form.other_fees.id_for_label }}" class="form-label">Other Fees</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            {{ form.other_fees }}
                        </div>
                        <p class="form-help">Other miscellaneous fees</p>
                    </div>
                </div>

                <!-- Total Cost Calculation -->
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="font-medium">Total Cost (including fees):</span>
                        <span id="total-cost" class="text-lg font-bold text-blue-600">$0.00</span>
                    </div>
                </div>
            </div>

            <!-- Execution Details -->
            <div class="form-section">
                <h2 class="section-title">Execution Details</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="form-field">
                        <label for="{{ form.order_type.id_for_label }}" class="form-label">Order Type</label>
                        {{ form.order_type }}
                    </div>

                    <div class="form-field">
                        <label for="{{ form.execution_time.id_for_label }}" class="form-label">Execution Time</label>
                        {{ form.execution_time }}
                        <p class="form-help">Time when the order was executed (optional)</p>
                    </div>

                    <div class="form-field">
                        <div class="flex items-center">
                            {{ form.is_short }}
                            <label for="{{ form.is_short.id_for_label }}" class="ml-2 form-label">Short Position</label>
                        </div>
                        <p class="form-help">Check if this is a short position</p>
                    </div>
                </div>
            </div>

            <!-- Ownership Splits -->
            <div class="form-section">
                <h2 class="section-title">Ownership Splits</h2>
                <p class="text-sm text-gray-600 mb-4">
                    Specify how this transaction is split among different owners. Percentages must total 100%.
                </p>

                <div id="ownership-splits">
                    {% for ownership in ownerships %}
                    <div class="ownership-split-row">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                            <div class="form-field">
                                <label class="form-label">Owner</label>
                                <select name="owners" class="form-input">
                                    <option value="">Select Owner</option>
                                    {% for owner in owners %}
                                        <option value="{{ owner.id }}" {% if ownership.owner.id == owner.id %}selected{% endif %}>{{ owner.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="form-field">
                                <label class="form-label">Percentage</label>
                                <div class="relative">
                                    <input type="number" name="percentages" step="0.01" min="0" max="100"
                                           value="{{ ownership.percentage }}" class="form-input pr-8"
                                           placeholder="0.00" onchange="updateOwnershipTotals()">
                                    <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
                                </div>
                            </div>

                            <div class="form-field">
                                <button type="button" class="btn-delete remove-split" onclick="removeSplit(this)">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    Remove
                                </button>
                            </div>
                        </div>
                        {% if ownership.id %}<input type="hidden" name="ownership_ids" value="{{ ownership.id }}">{% endif %}
                    </div>
                    {% empty %}
                    <div class="ownership-split-row">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                            <div class="form-field">
                                <label class="form-label">Owner</label>
                                <select name="owners" class="form-input">
                                    <option value="">Select Owner</option>
                                    {% for owner in owners %}
                                        <option value="{{ owner.id }}">{{ owner.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="form-field">
                                <label class="form-label">Percentage</label>
                                <div class="relative">
                                    <input type="number" name="percentages" step="0.01" min="0" max="100"
                                           class="form-input pr-8" placeholder="100.00" value="100"
                                           onchange="updateOwnershipTotals()">
                                    <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
                                </div>
                            </div>

                            <div class="form-field">
                                <button type="button" class="btn-delete remove-split" onclick="removeSplit(this)">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    Remove
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <div class="flex justify-between items-center mt-4">
                    <button type="button" id="add-owner-split" class="btn-secondary">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Add Owner Split
                    </button>

                    <div class="text-sm">
                        <span class="text-gray-600">Total: </span>
                        <span id="ownership-total" class="font-medium">100.00%</span>
                        <span id="ownership-warning" class="text-red-600 ml-2 hidden">⚠ Must equal 100%</span>
                    </div>
                </div>
            </div>

            <!-- Notes & Additional Information -->
            <div class="form-section">
                <h2 class="section-title">Additional Information</h2>
                <div class="grid grid-cols-1 gap-6">
                    <div class="form-field">
                        <label for="{{ form.trade_reason.id_for_label }}" class="form-label">Trade Reason</label>
                        {{ form.trade_reason }}
                        <p class="form-help">Why did you make this trade? (for trade journaling)</p>
                    </div>

                    <div class="form-field">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                        {{ form.notes }}
                        <p class="form-help">Additional notes about this transaction</p>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="submit" class="btn-save">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    {% if object %}Update Transaction{% else %}Save Transaction{% endif %}
                </button>

                <a href="{% url 'transaction_list' %}" class="btn-cancel">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel
                </a>

                {% if object %}
                <a href="{% url 'transaction_delete' object.pk %}" class="btn-delete ml-auto"
                   onclick="return confirm('Are you sure you want to delete this transaction?')">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Delete
                </a>
                {% endif %}
            </div>
        </form>
    </div>
</div>

{% endblock investments_content %}

{% block investments_js %}
<script>
// Form calculations and interactions
document.addEventListener('DOMContentLoaded', function() {
    // Initialize calculations
    updateCalculations();
    updateOwnershipTotals();

    // Add event listeners for real-time calculations
    const quantityField = document.getElementById('{{ form.quantity.id_for_label }}');
    const priceField = document.getElementById('{{ form.price.id_for_label }}');
    const feeFields = [
        document.getElementById('{{ form.fees.id_for_label }}'),
        document.getElementById('{{ form.commission.id_for_label }}'),
        document.getElementById('{{ form.sec_fee.id_for_label }}'),
        document.getElementById('{{ form.other_fees.id_for_label }}')
    ];

    [quantityField, priceField, ...feeFields].forEach(field => {
        if (field) {
            field.addEventListener('input', updateCalculations);
        }
    });

    // Add owner split functionality
    document.getElementById('add-owner-split').addEventListener('click', addOwnerSplit);

    // Form validation
    document.getElementById('transaction-form').addEventListener('submit', validateForm);
});

function updateCalculations() {
    const quantity = parseFloat(document.getElementById('{{ form.quantity.id_for_label }}').value) || 0;
    const price = parseFloat(document.getElementById('{{ form.price.id_for_label }}').value) || 0;
    const fees = parseFloat(document.getElementById('{{ form.fees.id_for_label }}').value) || 0;
    const commission = parseFloat(document.getElementById('{{ form.commission.id_for_label }}').value) || 0;
    const secFee = parseFloat(document.getElementById('{{ form.sec_fee.id_for_label }}').value) || 0;
    const otherFees = parseFloat(document.getElementById('{{ form.other_fees.id_for_label }}').value) || 0;

    const grossAmount = quantity * price;
    const totalFees = fees + commission + secFee + otherFees;
    const totalCost = grossAmount + totalFees;

    document.getElementById('gross-amount').textContent = '$' + grossAmount.toFixed(2);
    document.getElementById('total-cost').textContent = '$' + totalCost.toFixed(2);
}

function addOwnerSplit() {
    const container = document.getElementById('ownership-splits');
    const newSplit = document.createElement('div');
    newSplit.className = 'ownership-split-row';
    newSplit.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div class="form-field">
                <label class="form-label">Owner</label>
                <select name="owners" class="form-input">
                    <option value="">Select Owner</option>
                    {% for owner in owners %}
                        <option value="{{ owner.id }}">{{ owner.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="form-field">
                <label class="form-label">Percentage</label>
                <div class="relative">
                    <input type="number" name="percentages" step="0.01" min="0" max="100"
                           class="form-input pr-8" placeholder="0.00" onchange="updateOwnershipTotals()">
                    <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
                </div>
            </div>

            <div class="form-field">
                <button type="button" class="btn-delete remove-split" onclick="removeSplit(this)">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Remove
                </button>
            </div>
        </div>
    `;
    container.appendChild(newSplit);
    updateOwnershipTotals();
}

function removeSplit(button) {
    const splitRow = button.closest('.ownership-split-row');
    const container = document.getElementById('ownership-splits');

    // Don't allow removing the last split
    if (container.children.length > 1) {
        splitRow.remove();
        updateOwnershipTotals();
    } else {
        alert('At least one ownership split is required.');
    }
}

function updateOwnershipTotals() {
    const percentageInputs = document.querySelectorAll('input[name="percentages"]');
    let total = 0;

    percentageInputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        total += value;
    });

    const totalElement = document.getElementById('ownership-total');
    const warningElement = document.getElementById('ownership-warning');

    totalElement.textContent = total.toFixed(2) + '%';

    if (Math.abs(total - 100) > 0.01) {
        totalElement.classList.add('text-red-600');
        totalElement.classList.remove('text-green-600');
        warningElement.classList.remove('hidden');
    } else {
        totalElement.classList.add('text-green-600');
        totalElement.classList.remove('text-red-600');
        warningElement.classList.add('hidden');
    }
}

function validateForm(event) {
    const percentageInputs = document.querySelectorAll('input[name="percentages"]');
    let total = 0;
    let hasOwners = false;

    percentageInputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        total += value;
        if (value > 0) hasOwners = true;
    });

    if (hasOwners && Math.abs(total - 100) > 0.01) {
        event.preventDefault();
        alert('Ownership percentages must total exactly 100%');
        return false;
    }

    return true;
}

function openAssetModal() {
    // Placeholder for asset creation modal
    alert('Asset creation modal will be implemented in the next phase.');
}

// Auto-format currency inputs
document.addEventListener('DOMContentLoaded', function() {
    const currencyInputs = document.querySelectorAll('input[type="number"][step]');
    currencyInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !isNaN(this.value)) {
                this.value = parseFloat(this.value).toFixed(2);
            }
        });
    });
});
</script>
{% endblock investments_js %}