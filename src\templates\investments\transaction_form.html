{% extends "dashboard/base.html" %}
{% load static %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-4">{% if object %}Edit{% else %}Add{% endif %} Transaction</h1>
    <form method="post" class="space-y-6">
        {% csrf_token %}
        {{ form.as_p }}

        <!-- Ownership Splits -->
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Ownership Splits</h3>
            {% if form.errors %}
                <p class="text-red-600">{{ form.errors }}</p>
            {% endif %}
            {% for ownership in ownerships %}
            <div class="flex space-x-4 mb-2">
                <select name="owners" class="w-1/2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <option value="">Select Owner</option>
                    {% for owner in owners %}
                        <option value="{{ owner.id }}" {% if ownership.owner.id == owner.id %}selected{% endif %}>{{ owner.name }}</option>
                    {% endfor %}
                </select>
                <input type="number" name="percentages" step="0.01" min="0" max="100" value="{{ ownership.percentage }}" class="w-1/4 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" placeholder="Percentage (0-100)">
                {% if ownership.id %}<input type="hidden" name="ownership_ids" value="{{ ownership.id }}">{% endif %}
            </div>
            {% empty %}
            <div class="flex space-x-4 mb-2">
                <select name="owners" class="w-1/2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <option value="">Select Owner</option>
                    {% for owner in owners %}
                        <option value="{{ owner.id }}">{{ owner.name }}</option>
                    {% endfor %}
                </select>
                <input type="number" name="percentages" step="0.01" min="0" max="100" class="w-1/4 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" placeholder="Percentage (0-100)">
            </div>
            {% endfor %}
            <button type="button" id="add-owner-split" class="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">Add Split</button>
        </div>

        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">Save</button>
        <a href="{% url 'transaction_list' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">Cancel</a>
    </form>
</div>

<script>
document.getElementById('add-owner-split').addEventListener('click', function() {
    const container = this.parentElement;
    const newSplit = document.createElement('div');
    newSplit.className = 'flex space-x-4 mb-2';
    newSplit.innerHTML = `
        <select name="owners" class="w-1/2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Select Owner</option>
            {% for owner in owners %}
                <option value="{{ owner.id }}">{{ owner.name }}</option>
            {% endfor %}
        </select>
        <input type="number" name="percentages" step="0.01" min="0" max="100" class="w-1/4 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" placeholder="Percentage (0-100)">
    `;
    container.insertBefore(newSplit, this);
});
</script>
{% endblock %}