"""
URL configuration for cfehome project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include

from .views import pw_protectecd_view, user_only_view, staff_only_view, about_page_view
from landing.views import landing_dashboard_page_view
from contact.views import contact_view
from auth import views as auth_views
# from notifications.views import test_cel
from activity_log.views import extend_session, check_session

handler404 = 'cfehome.views.custom_404'

admin.site.site_title = "Admin-PennyMize"
admin.site.site_header = "PennyMize administration"
admin.site.index_title = "PennyMize administration"

urlpatterns = [
    path("pisf/", admin.site.urls),
    path("", landing_dashboard_page_view, name='home'), # index_page
    path("contact/", contact_view, name='contact'), # contact_page
    # path("test/", test, name='test'), # notification trial
    # path("cel/", test_cel, name='cel'), # celery trial
    # path("about/", about_view),
    path("about/", about_page_view, name='about'), # about_page
    # path("login/", auth_views.login_view),
    # path("register/", auth_views.register_view),
    path("accounts/", include("allauth.urls")),
    path("finance/", include("finance.urls")),
    path("investments/", include("investments.urls")),
    # path("protected/", pw_protectecd_view),
    # path("protected/user-only", user_only_view),
    # path("protected/staff-only", staff_only_view),
    path("profiles/", include("profiles.urls")),
    # path('extend-session/', extend_session, name='extend_session'),
    # path('check-session/', check_session, name='check_session'),
]
